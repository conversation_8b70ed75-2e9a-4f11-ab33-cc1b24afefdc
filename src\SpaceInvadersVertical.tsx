import React, { useRef, useEffect, useState, useCallback } from 'react';
import { useMutation, useAction } from 'convex/react';
import { api } from '../convex/_generated/api'; // Adjusted path assuming it's one level up from src
import { Id } from '../convex/_generated/dataModel'; // Adjusted path
import { 
  checkPlayerLevelUp, 
  getPlayerHealthBoostOnLevelUp, 
  getEnemySpawnCount 
} from './gameLogic/difficultyManager';
import BlueskyShare from './BlueskyShare';
import { 
  PowerUpLegend, 
  ControlsDisplay,
  ScoreSubmissionScreen,
  OptionsScreen,
  MenuScreen,
  GameOverScreen,
  GameScreen,
  MobileStatsDisplay
} from './components/gameUI/gameUi';
import { 
  usePauseGame, 
  PauseMenu, 
  PauseButton,
  useGameControls
} from './components/gameUI/features';
import { LeaderboardScreen } from './components/gameUI/leaderboards';
import MobileCanvas from './components/gameUI/mobileCanvas';

// Define all game object interfaces at the top level
interface GameObject {
  x: number;
  y: number;
  width: number;
  height: number;
  vx?: number;
  vy?: number;
}

interface Player extends GameObject {
  health: number;
  maxHealth: number;
  fireRate: number;
  lastShot: number;
  powerLevel: number;
  activeMultiShotLevel: number;
  multiShotLevelExpireTime: number;
  autoHealCharges: number;
}

interface Enemy extends GameObject {
  type: 'basic' | 'fast' | 'heavy' | 'boss';
  health: number;
  maxHealth: number;
  points: number;
  lastShot: number;
  fireRate: number;
  powerLevel?: number; 
  attackPattern?: 'multiSpread' | 'focusedBarrage';
  nextPatternSwitchTime?: number;
  isTelegraphing?: boolean;
  telegraphCompleteTime?: number;
  telegraphColor?: string;
}

interface Bullet extends GameObject {
  damage: number;
  isPlayerBullet: boolean;
}

interface PowerUp extends GameObject {
  type: 'health' | 'fireRate' | 'multiShot' | 'shield';
  duration?: number;
}

interface Particle extends GameObject {
  life: number;
  maxLife: number;
  color: string;
}

interface SpecialShip extends GameObject {
  type: 'barrierBoost' | 'autoHeal';
  health: number;
  dropDelayTimer: number; 
  hasDroppedCrate: boolean; 
  vx: number; 
}

interface Crate extends GameObject {
  type: 'barrierBoost' | 'autoHeal';
  spawnTime: number; 
  vy: number; 
}

interface GameInternalState {
  player: Player;
  enemies: Enemy[];
  bullets: Bullet[];
  powerUps: PowerUp[];
  particles: Particle[];
  keys: Record<string, boolean>;
  lastEnemySpawn: number;
  enemySpawnRate: number;
  lastTime: number;
  shakeIntensity: number;
  shakeDecay: number;
  isBossActiveRef: boolean;
  lastBossSpawnBlockRef: number;
  globalBossPowerLevelRef: number;
  barrierLine: number;
  specialShip: SpecialShip | null; 
  crate: Crate | null;             
  lastBarrierBoostSpawnLevelBlock: number; 
  lastAutoHealSpawnLevelBlock: number;    
}

export interface GameSettings {
  enemySpawnRate: number;
  playerFireRate: number;
  enemyBulletSpeed: number;
  playerBulletSpeed: number;
  musicVolume: number;
  sfxVolume: number;
  musicEnabled: boolean;
  sfxEnabled: boolean;
  controlScheme: 'arrows' | 'wasd';
}

export type GameState = 'menu' | 'playing' | 'gameOver' | 'options' | 'submitScore' | 'leaderboard'; 

interface SpaceInvadersVerticalProps { // Renamed Props interface
  initialDevState?: GameState;
  onGameStateChange?: (newState: GameState) => void;
  isMaximized: boolean; 
  setIsMaximized: (isMax: boolean) => void; 
}

// Vertical Mode: Base dimensions changed
const BASE_CANVAS_WIDTH = window.innerWidth; 
const BASE_CANVAS_HEIGHT = window.innerHeight;
let CANVAS_WIDTH = BASE_CANVAS_WIDTH; // Initial dynamic width
let CANVAS_HEIGHT = BASE_CANVAS_HEIGHT; // Initial dynamic height

const PLAYER_SPEED = 8; // Keep player speed for now
let canvasScale = 1; 

const DEFAULT_SETTINGS: GameSettings = {
  enemySpawnRate: 1.0,
  playerFireRate: 1.0,
  enemyBulletSpeed: 1.0,
  playerBulletSpeed: 1.0,
  musicVolume: 0.5,
  sfxVolume: 0.7,
  musicEnabled: true,
  sfxEnabled: true,
  controlScheme: 'arrows',
};

// Renamed component
export default function SpaceInvadersVertical({ 
  initialDevState, 
  onGameStateChange,
  isMaximized, 
  setIsMaximized 
}: SpaceInvadersVerticalProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null); 
  const overlayCanvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);
  const backgroundAnimationRef = useRef<number>(0);
  const crtAnimationRef = useRef<number>(0);
  const keyAnimationRef = useRef<{ left: number; right: number; space: number }>({ left: 0, right: 0, space: 0 });
  
  const [gameState, _setGameState] = useState<GameState>(initialDevState || 'menu');
  
  const [canvasDimsForStyle, setCanvasDimsForStyle] = useState({ 
    width: BASE_CANVAS_WIDTH, // Use new base for initial style
    height: BASE_CANVAS_HEIGHT 
  });

  const setGameState = useCallback((newState: GameState | ((prevState: GameState) => GameState)) => {
    _setGameState(prevState => {
      const resolvedState = typeof newState === 'function' ? newState(prevState) : newState;
      if (onGameStateChange) {
        onGameStateChange(resolvedState);
      }
      return resolvedState;
    });
  }, [onGameStateChange]);

  useEffect(() => {
    if (onGameStateChange) {
      onGameStateChange(gameState);
    }
  }, [gameState, onGameStateChange]);

  const [score, setScore] = useState(0);
  const [level, setLevel] = useState(1);
  const [enemiesKilled, setEnemiesKilled] = useState(0);
  const [settings, setSettings] = useState<GameSettings>(DEFAULT_SETTINGS);
  const [blueskyHandle, setBlueskyHandle] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationError, setVerificationError] = useState('');
  const [showConfirmAnonymous, setShowConfirmAnonymous] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  
  const audioRefs = useRef<{ 
    playerShoot: HTMLAudioElement | null;
    playerHit: HTMLAudioElement | null;
    enemyHit: HTMLAudioElement | null;
    enemyKill: HTMLAudioElement | null;
    powerupPickup: HTMLAudioElement | null;
    backgroundMusic: HTMLAudioElement | null;
  }>({
    playerShoot: null,
    playerHit: null,
    enemyHit: null,
    enemyKill: null,
    powerupPickup: null,
    backgroundMusic: null,
  });

  const { isPaused, togglePause, setIsPaused } = usePauseGame(gameState, audioRefs, settings); 
  
  const [savedScoreId, setSavedScoreId] = useState<Id<"scores"> | null>(null);
  const saveScore = useMutation(api.index.saveScore);
  const verifyBlueskyHandle = useAction(api.social.verifyBlueskyHandle);

  const gameStateRef = useRef<GameInternalState>({
    player: {
      // Player initial position adjusted for vertical mode
      x: BASE_CANVAS_WIDTH / 2 - 25, // Player width 50, so 25 is half
      y: BASE_CANVAS_HEIGHT - 60,   // Offset from bottom
      width: 50,
      height: 30,
      health: 100,
      maxHealth: 100, 
      fireRate: 300, 
      lastShot: 0,
      powerLevel: 1,
      activeMultiShotLevel: 0,
      multiShotLevelExpireTime: 0,
      autoHealCharges: 0,
    } as Player,
    enemies: [] as Enemy[],
    bullets: [] as Bullet[],
    powerUps: [] as PowerUp[],
    particles: [] as Particle[],
    keys: {} as Record<string, boolean>,
    lastEnemySpawn: 0,
    enemySpawnRate: 2000,
    lastTime: 0,
    shakeIntensity: 0,
    shakeDecay: 0.9,
    isBossActiveRef: false, 
    lastBossSpawnBlockRef: -1, 
    globalBossPowerLevelRef: 2, 
    barrierLine: 6, // This will behave differently due to taller canvas, review later
    specialShip: null,
    crate: null,
    lastBarrierBoostSpawnLevelBlock: -1,
    lastAutoHealSpawnLevelBlock: -1,
  });

  // updateCanvasDimensions logic remains the same, it will use the new BASE_CANVAS_WIDTH/HEIGHT
   useEffect(() => {
    const updateCanvasDimensions = () => {
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;
      
      const newEffectivelyMobile = screenWidth < 768 && !isMaximized; 
      if (newEffectivelyMobile !== isMobile) {
        setIsMobile(newEffectivelyMobile);
      }

      let newWidth: number;
      let newHeight: number;

      if (isMaximized) {
        const aspectRatio = BASE_CANVAS_WIDTH / BASE_CANVAS_HEIGHT; // Uses new base dimensions
        if (screenWidth / screenHeight > aspectRatio) {
          newHeight = screenHeight;
          newWidth = newHeight * aspectRatio;
        } else {
          newWidth = screenWidth;
          newHeight = newWidth / aspectRatio;
        }
      } else if (newEffectivelyMobile) {
        const aspectRatio = BASE_CANVAS_WIDTH / BASE_CANVAS_HEIGHT; // Uses new base dimensions
        const parentElement = canvasRef.current?.parentElement;
        const availableWidth = parentElement?.clientWidth || screenWidth;
        const availableHeight = Math.min(parentElement?.clientHeight || (screenHeight * 0.9), screenHeight * 0.9);

        if (availableWidth / availableHeight > aspectRatio) {
          newHeight = availableHeight;
          newWidth = newHeight * aspectRatio;
        } else {
          newWidth = availableWidth;
          newHeight = newWidth / aspectRatio;
        }
        newWidth = Math.min(newWidth, availableWidth);
        newHeight = Math.min(newHeight, availableHeight);

      } else {
        newWidth = BASE_CANVAS_WIDTH; // Uses new base dimensions
        newHeight = BASE_CANVAS_HEIGHT; // Uses new base dimensions
      }
      
      CANVAS_WIDTH = newWidth;
      CANVAS_HEIGHT = newHeight;
      canvasScale = CANVAS_WIDTH / BASE_CANVAS_WIDTH; 
      
      setCanvasDimsForStyle(prevDims => {
        if (prevDims.width === newWidth && prevDims.height === newHeight) {
          return prevDims;
        }
        return { width: newWidth, height: newHeight };
      });

      const canvasStyle = {
        width: `${newWidth}px`, 
        height: `${newHeight}px`,
        position: '' as const, 
        top: '',
        left: '',
        zIndex: ''
      };

      if (canvasRef.current) {
        canvasRef.current.width = newWidth; 
        canvasRef.current.height = newHeight; 
        Object.assign(canvasRef.current.style, canvasStyle);
      }
      if (overlayCanvasRef.current) {
        overlayCanvasRef.current.width = newWidth; 
        overlayCanvasRef.current.height = newHeight; 
        Object.assign(overlayCanvasRef.current.style, canvasStyle);
      }
    };

    updateCanvasDimensions(); 
    window.addEventListener('resize', updateCanvasDimensions);

    if (isMaximized || gameState === 'playing') {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = ''; 
    }

    return () => {
      window.removeEventListener('resize', updateCanvasDimensions);
      if (document.body && !isMaximized && gameState !== 'playing') {
         document.body.style.overflow = '';
      } else if (document.body) { 
         document.body.style.overflow = 'hidden';
      }
    };
  }, [gameState, isMaximized, isMobile]);


  useEffect(() => {
    const loadAudio = (filename: string) => {
      const audio = new Audio(`/audio/${filename}`);
      audio.preload = 'auto';
      audio.addEventListener('error', () => console.warn(`Could not load audio file: ${filename}`));
      return audio;
    };

    audioRefs.current = {
      playerShoot: loadAudio('player-shoot.mp3'),
      playerHit: loadAudio('player-hit.mp3'),
      enemyHit: loadAudio('enemy-hit.mp3'),
      enemyKill: loadAudio('enemy-kill.mp3'),
      powerupPickup: loadAudio('powerup-pickup.mp3'),
      backgroundMusic: loadAudio('background-music.mp3'),
    };

    if (audioRefs.current.backgroundMusic) {
      audioRefs.current.backgroundMusic.loop = true;
    }
  }, []);

  useEffect(() => {
    const animateKeys = () => {
      if (Math.random() < 0.01) keyAnimationRef.current.left = Date.now();
      if (Math.random() < 0.01) keyAnimationRef.current.right = Date.now();
      if (Math.random() < 0.005) keyAnimationRef.current.space = Date.now();
    };
    const interval = setInterval(animateKeys, 100);
    return () => clearInterval(interval);
  }, []);

  const playSound = useCallback((soundType: keyof typeof audioRefs.current) => {
    if (!settings.sfxEnabled && soundType !== 'backgroundMusic') return;
    if (!settings.musicEnabled && soundType === 'backgroundMusic') return;
    
    const audio = audioRefs.current[soundType];
    if (audio) {
      audio.volume = soundType === 'backgroundMusic' ? settings.musicVolume : settings.sfxVolume;
      if (soundType !== 'backgroundMusic') audio.currentTime = 0;
      audio.play().catch(() => {});
    }
  }, [settings]);

  const createParticles = useCallback((x: number, y: number, count: number, color: string) => {
    for (let i = 0; i < count; i++) {
      gameStateRef.current.particles.push({
        x: x + Math.random() * 20 - 10, y: y + Math.random() * 20 - 10,
        width: 2, height: 2,
        vx: (Math.random() - 0.5) * 8, vy: (Math.random() - 0.5) * 8,
        life: 60, maxLife: 60, color,
      });
    }
  }, []);

  const createEnemy = useCallback((type: Enemy['type'], x: number, y: number) => {
    const currentLevel = level; 
    const types = { // Enemy sizes (w, hgt) are absolute, may look different on portrait
      basic: { h: 1 , p: 10, fr: 2000, w: 30, hgt: 20, vxMod: 2, vyBase: 1 }, 
      fast:  { h: 1 , p: 20, fr: 1500, w: 25, hgt: 15, vxMod: 4, vyBase: 1.2 }, 
      heavy: { h: 3 * 2, p: 50, fr: 3000, w: 40, hgt: 30, vxMod: 1.5, vyBase: 0.8 }, 
      boss:  { h: 100 + currentLevel, p: 200,fr: 800,  w: 60, hgt: 40, vxMod: 1, vyBase: 0.5 }, 
    };
    const c = types[type];
    const enemyData: Enemy = {
      x, y, width: c.w, height: c.hgt,
      vx: (Math.random() - 0.5) * c.vxMod, vy: c.vyBase,
      type, health: c.h, maxHealth: c.h, points: c.p,
      lastShot: 0, fireRate: c.fr,
    };

    if (type === 'boss') {
      enemyData.powerLevel = gameStateRef.current.globalBossPowerLevelRef;
      enemyData.attackPattern = 'multiSpread'; 
      enemyData.nextPatternSwitchTime = 0; 
      enemyData.isTelegraphing = false;
      enemyData.telegraphCompleteTime = 0;
      enemyData.telegraphColor = undefined;
    }

    return enemyData;
  }, [level, gameStateRef]); 

  const spawnEnemies = useCallback((currentTime: number) => {
    const state = gameStateRef.current;
    const rate = state.enemySpawnRate / settings.enemySpawnRate;

    if (currentTime - state.lastEnemySpawn > rate) {
      let newBossSpawnedThisWave = false;
      if (level >= 20 && !state.isBossActiveRef) {
        const currentBlock = Math.floor((level - 20) / 10);
        if (currentBlock > state.lastBossSpawnBlockRef) {
          const levelInBlock = (level - 20) % 10; 
          const baseChance = 0.05; 
          const incrementPerLevel = 0.10;
          let spawnProbability = baseChance + (levelInBlock * incrementPerLevel);

          if (levelInBlock === 9) { 
            spawnProbability = 1.0;
          }
          
          if (Math.random() < spawnProbability) { 
            // Boss X position adjusted for vertical mode (boss width 60)
            const bossX = BASE_CANVAS_WIDTH / 2 - 30; 
            const bossY = -60; 
            const newBoss = createEnemy('boss', bossX, bossY);
            if (newBoss.type === 'boss') { 
                newBoss.nextPatternSwitchTime = currentTime + 8000; 
            }
            state.enemies.push(newBoss);
            state.isBossActiveRef = true;
            state.lastBossSpawnBlockRef = currentBlock;
            state.globalBossPowerLevelRef++; 
            newBossSpawnedThisWave = true; 
          }
        }
      }

      const regularEnemyBaseCount = getEnemySpawnCount(level);
      let actualRegularEnemyCount = regularEnemyBaseCount;

      if (state.isBossActiveRef) {
        actualRegularEnemyCount = Math.floor(regularEnemyBaseCount * 0.3);
      }
      
      if (!newBossSpawnedThisWave) { 
        for (let i = 0; i < actualRegularEnemyCount; i++) {
          // Enemy X spawn adjusted for vertical mode (assuming max enemy width around 60 for safety margin)
          const x = Math.random() * (BASE_CANVAS_WIDTH - 60); 
          const y = -50 - Math.random() * 200; // Y spawn remains the same
          let type: Enemy['type'] = 'basic';
          const r = Math.random(); 

          if (level > 14 && r < 0.3) { 
              type = 'heavy';
          } else if (level > 8 && r < 0.5) { 
              type = 'fast';
          }
          state.enemies.push(createEnemy(type, x, y));
        }
      }
      
      state.lastEnemySpawn = currentTime;
      state.enemySpawnRate = Math.max(800, 2000 - level * 100); 
    }
  }, [level, createEnemy, settings.enemySpawnRate]);

  const spawnSpecialBonuses = useCallback((currentTime: number) => {
    const state = gameStateRef.current;

    if (state.specialShip || state.crate) {
      return;
    }

    const SPECIAL_SHIP_WIDTH = 30;
    const SPECIAL_SHIP_HEIGHT = 20;
    const SPECIAL_SHIP_SPEED = 3;
    let bonusTypeToSpawn: SpecialShip['type'] | null = null;

    const barrierBoostBlockSize = 50;
    const barrierBoostSpawnWindowStart = 45;
    const barrierBoostSpawnWindowEnd = 50;
    const currentBarrierBlock = Math.floor((level - 1) / barrierBoostBlockSize);
    const levelWithinBarrierBlock = ((level - 1) % barrierBoostBlockSize) + 1;

    if (currentBarrierBlock > state.lastBarrierBoostSpawnLevelBlock &&
        levelWithinBarrierBlock >= barrierBoostSpawnWindowStart &&
        levelWithinBarrierBlock <= barrierBoostSpawnWindowEnd) {
      bonusTypeToSpawn = 'barrierBoost';
      state.lastBarrierBoostSpawnLevelBlock = currentBarrierBlock;
    }

    if (!bonusTypeToSpawn) {
      const autoHealBlockSize = 80;
      const autoHealSpawnWindowStart = 75;
      const autoHealSpawnWindowEnd = 85;
      const currentAutoHealBlock = Math.floor((level - 1) / autoHealBlockSize);
      const levelWithinAutoHealBlock = ((level - 1) % autoHealBlockSize) + 1;

      if (currentAutoHealBlock > state.lastAutoHealSpawnLevelBlock &&
          levelWithinAutoHealBlock >= autoHealSpawnWindowStart &&
          levelWithinAutoHealBlock <= autoHealSpawnWindowEnd) {
        bonusTypeToSpawn = 'autoHeal';
        state.lastAutoHealSpawnLevelBlock = currentAutoHealBlock;
      }
    }

    if (bonusTypeToSpawn) {
      const fliesFromLeft = Math.random() < 0.5;
      const newSpecialShip: SpecialShip = {
        // Special ship X spawn adjusted for vertical mode
        x: fliesFromLeft ? -SPECIAL_SHIP_WIDTH : BASE_CANVAS_WIDTH,
        y: Math.random() * (BASE_CANVAS_HEIGHT * 0.23) + (BASE_CANVAS_HEIGHT * 0.1), // Y range might need review for tall canvas
        width: SPECIAL_SHIP_WIDTH,
        height: SPECIAL_SHIP_HEIGHT,
        vx: fliesFromLeft ? SPECIAL_SHIP_SPEED : -SPECIAL_SHIP_SPEED,
        type: bonusTypeToSpawn,
        health: 1,
        dropDelayTimer: 0,
        hasDroppedCrate: false,
      };
      state.specialShip = newSpecialShip;
    }
  }, [level]); 

  const checkCollision = useCallback((a: GameObject, b: GameObject) => 
    a.x < b.x + b.width && a.x + a.width > b.x && a.y < b.y + b.height && a.y + a.height > b.y, 
  []);

  const { getMovementKeys, keyMap: gameKeyMap } = useGameControls(settings);

  const updateGame = useCallback((currentTime: number) => {
    const state = gameStateRef.current;
    const dt = currentTime - state.lastTime;
    state.lastTime = currentTime;
    backgroundAnimationRef.current += dt * 0.001;
    crtAnimationRef.current += dt * 0.01;

    const player = state.player;
    const moveKeys = getMovementKeys();

    if (player.activeMultiShotLevel > 0 && currentTime > player.multiShotLevelExpireTime) {
      player.activeMultiShotLevel--; 
      if (player.activeMultiShotLevel > 0) {
        player.multiShotLevelExpireTime = currentTime + 10000; 
      } else {
        player.multiShotLevelExpireTime = 0; 
      }
    }
    player.powerLevel = 1 + player.activeMultiShotLevel; 
    
    const effectivePlayerSpeed = PLAYER_SPEED; 

    if (isMobile) {
      if (state.keys['touchLeft'] && player.x > 0) player.x -= effectivePlayerSpeed;
      // Player right movement limit adjusted for vertical mode
      if (state.keys['touchRight'] && player.x < BASE_CANVAS_WIDTH - player.width) player.x += effectivePlayerSpeed;
      if (currentTime - player.lastShot > player.fireRate / settings.playerFireRate) {
        for (let i = 0; i < player.powerLevel; i++) {
          const offX = player.powerLevel > 1 ? (i - (player.powerLevel - 1) / 2) * 35 : 0; 
          state.bullets.push({ x: player.x + player.width / 2 - 2 + offX, y: player.y, width: 4, height: 10, vy: -12 * settings.playerBulletSpeed, damage: 1, isPlayerBullet: true });
        }
        player.lastShot = currentTime;
        playSound('playerShoot');
      }
    } else {
      if (state.keys[moveKeys.left] && player.x > 0) player.x -= effectivePlayerSpeed;
      // Player right movement limit adjusted for vertical mode
      if (state.keys[moveKeys.right] && player.x < BASE_CANVAS_WIDTH - player.width) player.x += effectivePlayerSpeed;
      if (state.keys[moveKeys.shoot] && currentTime - player.lastShot > player.fireRate / settings.playerFireRate) {
         for (let i = 0; i < player.powerLevel; i++) {
          const offX = player.powerLevel > 1 ? (i - (player.powerLevel - 1) / 2) * 35 : 0; 
          state.bullets.push({ x: player.x + player.width / 2 - 2 + offX, y: player.y, width: 4, height: 10, vy: -12 * settings.playerBulletSpeed, damage: 1, isPlayerBullet: true });
        }
        player.lastShot = currentTime;
        playSound('playerShoot');
      }
    }

    if (!isMobile && state.keys['h']) {
      if (player.autoHealCharges > 0 && player.health < player.maxHealth) {
        const AUTO_HEAL_AMOUNT = 50;
        player.health = Math.min(player.maxHealth, player.health + AUTO_HEAL_AMOUNT);
        player.autoHealCharges--;
        playSound('powerupPickup'); 
        createParticles(player.x + player.width / 2, player.y + player.height / 2, 15, '#88ff88'); 
        state.keys['h'] = false; 
      } else {
        state.keys['h'] = false;
      }
    }

    spawnEnemies(currentTime);
    spawnSpecialBonuses(currentTime); 

    if (state.specialShip) {
      state.specialShip.x += state.specialShip.vx;
      // Special ship horizontal boundary check adjusted
      if (state.specialShip.vx > 0 && state.specialShip.x > BASE_CANVAS_WIDTH) {
        state.specialShip = null;
      } else if (state.specialShip.vx < 0 && state.specialShip.x + state.specialShip.width < 0) {
        state.specialShip = null;
      }
      else if (
        state.specialShip && 
        state.specialShip.dropDelayTimer > 0 &&
        currentTime >= state.specialShip.dropDelayTimer &&
        !state.specialShip.hasDroppedCrate
      ) {
        if (!state.crate) {
          const CRATE_WIDTH = 20;
          const CRATE_HEIGHT = 20;
          const CRATE_FALL_SPEED = 2;
          const newCrate: Crate = {
            x: state.specialShip.x + state.specialShip.width / 2 - CRATE_WIDTH / 2,
            y: state.specialShip.y + state.specialShip.height / 2 - CRATE_HEIGHT / 2,
            width: CRATE_WIDTH,
            height: CRATE_HEIGHT,
            vy: CRATE_FALL_SPEED,
            type: state.specialShip.type,
            spawnTime: currentTime,
          };
          state.crate = newCrate;
          playSound('powerupPickup'); 
        }
        state.specialShip.hasDroppedCrate = true; 
        state.specialShip = null; 
      }
    }

    if (state.crate) {
      state.crate.y += state.crate.vy;
      if (checkCollision(state.player, state.crate)) {
        if (state.crate.type === 'barrierBoost') {
          state.barrierLine = Math.max(0, state.barrierLine - 5); 
        } else if (state.crate.type === 'autoHeal') {
          state.player.autoHealCharges = Math.min(3, (state.player.autoHealCharges || 0) + 1);
        }
        createParticles(state.crate.x + state.crate.width / 2, state.crate.y + state.crate.height / 2, 15, '#ffff88'); 
        playSound('powerupPickup'); 
        state.crate = null; 
      } 
      else if (state.crate.y > BASE_CANVAS_HEIGHT) { // Uses new BASE_CANVAS_HEIGHT
        state.crate = null;
      }
    }

    const BARRIER_LINE_HEIGHT_DIVISOR = 12; // This will result in a different pixel Y due to taller canvas
    const barrierPixelY = state.barrierLine * (BASE_CANVAS_HEIGHT / BARRIER_LINE_HEIGHT_DIVISOR);

    state.enemies.forEach((e: Enemy) => { 
      const intendedY = e.y + (e.vy || 0);
      if (e.vy && e.vy > 0 && (e.y + e.height) < barrierPixelY && (intendedY + e.height) >= barrierPixelY) {
        e.y = barrierPixelY - e.height; 
        e.vy = 0; 
      } else if ((e.y + e.height) >= barrierPixelY) {
        e.y = barrierPixelY - e.height;
        if (e.vy && e.vy > 0) e.vy = 0;
      } else {
        e.y = intendedY;
      }
      e.x += e.vx || 0;
      // Enemy horizontal boundary check adjusted
      if (e.x <= 0 || e.x >= BASE_CANVAS_WIDTH - e.width) e.vx = -(e.vx || 0);
      if (e.type === 'boss') {
        const TELEGRAPH_DURATION = 1000; 
        const PATTERN_SWITCH_INTERVAL = 8000; 
        if (e.attackPattern === undefined || e.nextPatternSwitchTime === undefined || e.isTelegraphing === undefined || e.telegraphCompleteTime === undefined) {
            e.attackPattern = 'multiSpread';
            e.nextPatternSwitchTime = currentTime + PATTERN_SWITCH_INTERVAL;
            e.isTelegraphing = false;
            e.telegraphCompleteTime = 0;
            e.telegraphColor = undefined;
        }
        if (currentTime > e.nextPatternSwitchTime && !e.isTelegraphing) {
          e.attackPattern = e.attackPattern === 'multiSpread' ? 'focusedBarrage' : 'multiSpread';
          e.nextPatternSwitchTime = currentTime + PATTERN_SWITCH_INTERVAL;
          e.isTelegraphing = true;
          e.telegraphCompleteTime = currentTime + TELEGRAPH_DURATION;
          e.telegraphColor = e.attackPattern === 'multiSpread' ? 'rgba(255, 100, 100, 0.7)' : 'rgba(100, 100, 255, 0.7)';
        }
        if (currentTime - e.lastShot > e.fireRate) {
          let canFireNow = false;
          if (e.isTelegraphing) {
            if (currentTime > e.telegraphCompleteTime) {
              e.isTelegraphing = false;
              e.telegraphColor = undefined;
              canFireNow = true; 
            }
          } else {
            canFireNow = true; 
          }
          if (canFireNow && e.powerLevel && e.powerLevel > 0) {
            if (e.attackPattern === 'multiSpread') {
              for (let i = 0; i < e.powerLevel; i++) {
                const offX = e.powerLevel > 1 ? (i - (e.powerLevel - 1) / 2) * 20 : 0;
                state.bullets.push({
                  x: e.x + e.width / 2 - 2 + offX, y: e.y + e.height,
                  width: 4, height: 10, vy: 5 * settings.enemyBulletSpeed,
                  damage: 15, isPlayerBullet: false,
                });
              }
            } else if (e.attackPattern === 'focusedBarrage') {
              for (let i = 0; i < e.powerLevel; i++) {
                const offX = (i - (e.powerLevel - 1) / 2) * 5; 
                state.bullets.push({
                  x: e.x + e.width / 2 - 2 + offX, y: e.y + e.height,
                  width: 3, height: 12, 
                  vy: 7 * settings.enemyBulletSpeed, 
                  damage: 12, 
                  isPlayerBullet: false,
                });
              }
            }
            e.lastShot = currentTime;
          }
        }
      } else { 
        if (currentTime - e.lastShot > e.fireRate) {
          if (Math.random() < 0.005) { 
            state.bullets.push({
              x: e.x + e.width / 2 - 2, y: e.y + e.height,
              width: 4, height: 8, vy: 6 * settings.enemyBulletSpeed,
              damage: 10, isPlayerBullet: false,
            });
            e.lastShot = currentTime;
          }
        }
      }
    });

    state.bullets.forEach((b: Bullet) => b.y += b.vy || 0); 
    state.particles.forEach((p: Particle) => { p.x += p.vx || 0; p.y += p.vy || 0; p.life--; }); 

    state.bullets.forEach((bullet: Bullet, bIdx: number) => { 
      if (bullet.isPlayerBullet) {
        state.enemies.forEach((enemy: Enemy, eIdx: number) => { 
          if (checkCollision(bullet, enemy)) {
            enemy.health -= bullet.damage;
            state.bullets.splice(bIdx, 1);
            createParticles(enemy.x + enemy.width / 2, enemy.y + enemy.height / 2, 8, 'var(--red-secondary-ff4444)');
            playSound('enemyHit');
            if (enemy.health <= 0) {
              setScore(s => s + enemy.points);
              setEnemiesKilled(ek => ek + 1);
              if (enemy.type === 'boss') {
                state.isBossActiveRef = false; 
                const BARRIER_MAX_LINE = 11; // This might need review for portrait
                if (state.barrierLine < BARRIER_MAX_LINE) {
                  state.barrierLine++;
                }
              }
              state.enemies.splice(eIdx, 1);
              createParticles(enemy.x + enemy.width / 2, enemy.y + enemy.height / 2, 15, 'var(--red-main-ff0000)');
              state.shakeIntensity = 5;
              playSound('enemyKill');
              if (Math.random() < 0.15) { 
                const types: PowerUp['type'][] = ['health', 'fireRate', 'multiShot'];
                state.powerUps.push({ x: enemy.x, y: enemy.y, width: 20, height: 20, vy: 2, type: types[Math.floor(Math.random() * types.length)] });
              }
            }
          }
        });
        if (state.specialShip && !state.specialShip.hasDroppedCrate && state.specialShip.dropDelayTimer === 0 && checkCollision(bullet, state.specialShip)) {
          state.specialShip.health -= bullet.damage;
          state.bullets.splice(bIdx, 1); 
          createParticles(state.specialShip.x + state.specialShip.width / 2, state.specialShip.y + state.specialShip.height / 2, 8, '#88ddff'); 
          playSound('enemyHit'); 
          if (state.specialShip.health <= 0) {
            const DROP_DELAY = 500; 
            state.specialShip.dropDelayTimer = currentTime + DROP_DELAY;
          }
        }
      } else if (checkCollision(bullet, player)) {
        player.health -= bullet.damage;
        state.bullets.splice(bIdx, 1);
        createParticles(player.x + player.width / 2, player.y + player.height / 2, 5, '#ff6666');
        state.shakeIntensity = 8;
        playSound('playerHit');
      }
    });

    state.powerUps.forEach((pUp: PowerUp, idx: number) => { 
      pUp.y += pUp.vy || 0;
      if (checkCollision(pUp, player)) {
        if (pUp.type === 'health') player.health = Math.min(player.maxHealth, player.health + 30);
        else if (pUp.type === 'fireRate') player.fireRate = Math.max(50, player.fireRate - 30);
        else if (pUp.type === 'multiShot') {
          player.activeMultiShotLevel = Math.min(5, player.activeMultiShotLevel + 1); 
          player.multiShotLevelExpireTime = currentTime + 10000; 
        }
        state.powerUps.splice(idx, 1);
        createParticles(pUp.x + 10, pUp.y + 10, 10, '#ff8888');
        playSound('powerupPickup');
      }
    });

    // Filter objects based on new BASE_CANVAS_HEIGHT
    state.bullets = state.bullets.filter((b: Bullet) => b.y > -20 && b.y < BASE_CANVAS_HEIGHT + 20); 
    state.enemies = state.enemies.filter((e: Enemy) => e.y < BASE_CANVAS_HEIGHT + 50); 
    state.powerUps = state.powerUps.filter((p: PowerUp) => p.y < BASE_CANVAS_HEIGHT); 
    state.particles = state.particles.filter((p: Particle) => p.life > 0); 
    state.shakeIntensity *= state.shakeDecay;

    if (checkPlayerLevelUp(enemiesKilled, level)) { 
        setLevel(l => l + 1);
        player.health = Math.min(player.maxHealth, player.health + getPlayerHealthBoostOnLevelUp()); 
    }

    if (player.health <= 0) {
      const isGodMode = import.meta.env.DEV && localStorage.getItem('devGodMode') === 'true';
      if (isGodMode) {
        player.health = player.maxHealth; 
        console.log('[DevUtils] God Mode: Player health restored!');
        createParticles(player.x + player.width / 2, player.y + player.height / 2, 20, '#00ff00'); 
      } else {
        setGameState('submitScore');
        if (audioRefs.current.backgroundMusic) audioRefs.current.backgroundMusic.pause();
      }
    }
  }, [checkCollision, createParticles, spawnEnemies, enemiesKilled, level, settings, playSound, getMovementKeys, isMobile]);

  const renderCRTOverlay = useCallback(() => {
    const overlay = overlayCanvasRef.current;
    if (!overlay || isMobile) return; // CRT might look odd on very tall canvas, but keep for now
    const ctx = overlay.getContext('2d');
    if (!ctx) return;
    ctx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT); 
    const time = crtAnimationRef.current;
    const grad = ctx.createRadialGradient(CANVAS_WIDTH/2, CANVAS_HEIGHT/2, 0, CANVAS_WIDTH/2, CANVAS_HEIGHT/2, Math.max(CANVAS_WIDTH, CANVAS_HEIGHT)*0.8);
    grad.addColorStop(0, 'rgba(0,0,0,0)'); grad.addColorStop(0.7, 'rgba(0,0,0,0.1)'); grad.addColorStop(1, 'rgba(0,0,0,0.6)');
    ctx.fillStyle = grad; ctx.fillRect(0,0,CANVAS_WIDTH,CANVAS_HEIGHT);
    ctx.fillStyle = 'rgba(0,0,0,0.15)'; for(let i=0; i<CANVAS_HEIGHT; i+=3) ctx.fillRect(0,i,CANVAS_WIDTH,1);
    const scanY = (time*2)%CANVAS_HEIGHT; ctx.fillStyle='rgba(255,255,255,0.05)'; ctx.fillRect(0,scanY,CANVAS_WIDTH,2);
    ctx.globalCompositeOperation='screen';
    ctx.fillStyle='rgba(255,0,0,0.02)'; ctx.fillRect(1,0,CANVAS_WIDTH,CANVAS_HEIGHT);
    ctx.fillStyle='rgba(0,255,0,0.02)'; ctx.fillRect(-1,0,CANVAS_WIDTH,CANVAS_HEIGHT);
    ctx.fillStyle='rgba(0,0,255,0.02)'; ctx.fillRect(0,1,CANVAS_WIDTH,CANVAS_HEIGHT);
    ctx.globalCompositeOperation='source-over';
    if (Math.random() < 0.01) { ctx.fillStyle = `rgba(255,255,255,${Math.random()*0.1})`; ctx.fillRect(0,0,CANVAS_WIDTH,CANVAS_HEIGHT); }
  }, [isMobile]);

  const render = useCallback(() => {
    const canvas = canvasRef.current; if (!canvas) return;
    const ctx = canvas.getContext('2d'); if (!ctx) return;
    const state = gameStateRef.current;

    ctx.save();
    if (state.shakeIntensity > 0.1) ctx.translate((Math.random()-0.5)*state.shakeIntensity, (Math.random()-0.5)*state.shakeIntensity);
    
    const grad = ctx.createLinearGradient(0,0,0,CANVAS_HEIGHT); const t=backgroundAnimationRef.current;
    const w1=Math.sin(t*0.5)*0.1+0.1; const w2=Math.sin(t*0.3)*0.05+0.05;
    grad.addColorStop(0,`rgba(${Math.floor(10+w1*20)},0,0,1)`); grad.addColorStop(0.3,`rgba(${Math.floor(20+w2*30)},0,0,0.8)`);
    grad.addColorStop(0.7,`rgba(${Math.floor(15+w1*25)},0,0,0.6)`); grad.addColorStop(1,`rgba(${Math.floor(5+w2*15)},0,0,1)`);
    ctx.fillStyle=grad; ctx.fillRect(0,0,CANVAS_WIDTH,CANVAS_HEIGHT);

    ctx.strokeStyle='rgba(255,0,0,0.1)'; ctx.lineWidth=1*canvasScale;
    // Background lines might look different on tall canvas
    for(let i=0;i<10;i++){ const yb=(t*20+i*60)%(BASE_CANVAS_HEIGHT+60); const ys=yb*canvasScale; ctx.beginPath();ctx.moveTo(0,ys);ctx.lineTo(CANVAS_WIDTH,ys);ctx.stroke(); }

    const BARRIER_LINE_HEIGHT_DIVISOR = 12; // Barrier position will be different
    const BARRIER_COLOR = '#00FFFF'; 
    const BARRIER_THICKNESS = 3; 
    const barrierYPosition = (state.barrierLine * (BASE_CANVAS_HEIGHT / BARRIER_LINE_HEIGHT_DIVISOR));
    
    ctx.fillStyle = BARRIER_COLOR;
    ctx.shadowColor = BARRIER_COLOR;
    ctx.shadowBlur = 5 * canvasScale;
    ctx.fillRect(0, barrierYPosition * canvasScale - (BARRIER_THICKNESS * canvasScale / 2), CANVAS_WIDTH, BARRIER_THICKNESS * canvasScale);
    ctx.shadowBlur = 0; 

    const p=state.player;
    let determinedPlayerColor = '#ff0000'; 
    let determinedPlayerInnerColor = '#ff4444';

    if (p.powerLevel === 2) {
      determinedPlayerColor = '#0000ff'; 
      determinedPlayerInnerColor = '#4444ff';
    } else if (p.powerLevel === 3) {
      determinedPlayerColor = '#00ff00'; 
      determinedPlayerInnerColor = '#44ff44';
    } else if (p.powerLevel === 4) {
      determinedPlayerColor = '#ffff00'; 
      determinedPlayerInnerColor = '#ffff44';
    } else if (p.powerLevel >= 5) { 
      determinedPlayerColor = '#ff00ff'; 
      determinedPlayerInnerColor = '#ff44ff';
    }

    ctx.fillStyle = determinedPlayerColor; ctx.shadowColor = determinedPlayerColor; ctx.shadowBlur=10*canvasScale;
    ctx.fillRect(p.x*canvasScale,p.y*canvasScale,p.width*canvasScale,p.height*canvasScale);
    ctx.fillStyle=determinedPlayerInnerColor; ctx.fillRect((p.x+5)*canvasScale,(p.y+5)*canvasScale,(p.width-10)*canvasScale,(p.height-10)*canvasScale);
    ctx.shadowBlur = 0; 

    state.enemies.forEach((e: Enemy)=>{ 
      let c='#ff3333'; if(e.type==='fast')c='#ff6666';else if(e.type==='heavy')c='#cc0000';else if(e.type==='boss')c='#990000';
      ctx.fillStyle=c;ctx.shadowColor=c;ctx.shadowBlur=8*canvasScale;
      ctx.fillRect(e.x*canvasScale,e.y*canvasScale,e.width*canvasScale,e.height*canvasScale);
      if (e.type === 'boss' && e.isTelegraphing && e.telegraphColor) {
        ctx.save();
        ctx.fillStyle = e.telegraphColor;
        const telegraphProgress = (e.telegraphCompleteTime && e.telegraphCompleteTime > state.lastTime) 
                                  ? 1 - ((e.telegraphCompleteTime - state.lastTime) / 1000) 
                                  : 1; 
        const alpha = 0.3 + Math.sin(telegraphProgress * Math.PI * 4) * 0.2; 
        const baseColorMatch = e.telegraphColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/);
        if (baseColorMatch) {
            ctx.fillStyle = `rgba(${baseColorMatch[1]}, ${baseColorMatch[2]}, ${baseColorMatch[3]}, ${Math.max(0.1, Math.min(0.5, alpha))})`;
        } else {
            ctx.fillStyle = e.telegraphColor;
        }
        ctx.fillRect(e.x*canvasScale,e.y*canvasScale,e.width*canvasScale,e.height*canvasScale);
        ctx.restore();
      }
      if(e.health<e.maxHealth){ // Health bar above enemy, Y position is relative to enemy Y
        ctx.fillStyle='#330000';ctx.fillRect(e.x*canvasScale,(e.y-8)*canvasScale,e.width*canvasScale,4*canvasScale);
        ctx.fillStyle='var(--red-main-ff0000)';ctx.fillRect(e.x*canvasScale,(e.y-8)*canvasScale,(e.health/e.maxHealth)*e.width*canvasScale,4*canvasScale);
      }
    });

    if (state.specialShip) {
      let shipColor = '#88ddff'; 
      if (state.specialShip.dropDelayTimer > 0 && !state.specialShip.hasDroppedCrate) {
        if (Math.floor(state.lastTime / 150) % 2 === 0) { 
          shipColor = '#ffffff'; 
        }
      }
      ctx.fillStyle = shipColor;
      ctx.shadowColor = shipColor;
      ctx.shadowBlur = 8 * canvasScale;
      ctx.fillRect(state.specialShip.x*canvasScale, state.specialShip.y*canvasScale, state.specialShip.width*canvasScale, state.specialShip.height*canvasScale);
      ctx.shadowBlur = 0;
    }

    if (state.crate) {
      let crateColor = '#dddddd'; 
      let crateSymbol = '?';
      if (state.crate.type === 'barrierBoost') {
        crateColor = '#00FFFF'; 
        crateSymbol = 'B';
      } else if (state.crate.type === 'autoHeal') {
        crateColor = '#88ff88'; 
        crateSymbol = 'H';
      }
      ctx.fillStyle = crateColor;
      ctx.shadowColor = crateColor;
      ctx.shadowBlur = 8 * canvasScale;
      ctx.fillRect(state.crate.x*canvasScale, state.crate.y*canvasScale, state.crate.width*canvasScale, state.crate.height*canvasScale);
      ctx.shadowBlur = 0;
      ctx.fillStyle = '#000000'; 
      ctx.font = `${14 * canvasScale}px VT323`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle'; 
      ctx.fillText(crateSymbol, (state.crate.x + state.crate.width/2)*canvasScale, (state.crate.y + state.crate.height/2)*canvasScale);
      ctx.textBaseline = 'alphabetic'; 
    }

    state.bullets.forEach((b: Bullet)=>{ 
      ctx.shadowColor=b.isPlayerBullet?'#ffff00':'#ff4444';ctx.shadowBlur=6*canvasScale; 
      ctx.fillStyle=b.isPlayerBullet?'#ffff00':'#ff4444';
      ctx.fillRect(b.x*canvasScale,b.y*canvasScale,b.width*canvasScale,b.height*canvasScale);
      ctx.shadowBlur = 0; 
    });
    state.powerUps.forEach((pu: PowerUp)=>{ 
      ctx.shadowColor='#ff8888';ctx.shadowBlur=8*canvasScale; 
      ctx.fillStyle='#ff8888';
      ctx.fillRect(pu.x*canvasScale,pu.y*canvasScale,pu.width*canvasScale,pu.height*canvasScale);
      ctx.shadowBlur = 0; 
      ctx.fillStyle='#ffffff';ctx.font=`${12*canvasScale}px VT323`;ctx.textAlign='center';
      const i=pu.type==='health'?'+':pu.type==='fireRate'?'F':pu.type==='multiShot'?'M':'S';
      ctx.fillText(i,(pu.x+10)*canvasScale,(pu.y+14)*canvasScale);
    });
    state.particles.forEach((pt: Particle)=>{ 
      const a=pt.life/pt.maxLife;ctx.fillStyle=pt.color+Math.floor(a*255).toString(16).padStart(2,'0');
      ctx.fillRect(pt.x*canvasScale,pt.y*canvasScale,pt.width*canvasScale,pt.height*canvasScale);
    });
    ctx.restore();

    // UI rendering adjustments for vertical mode
    ctx.fillStyle='var(--red-main-ff0000)'; 
    ctx.font=`${20*canvasScale}px VT323`; // Font size might need adjustment for narrow screen
    
    if (!isMobile) { 
      if (p.activeMultiShotLevel > 0) { 
        const DURATION_OF_BOOST = 10000; 
        const timeRemaining = p.multiShotLevelExpireTime - state.lastTime; 
        const fillRatio = Math.max(0, Math.min(1, timeRemaining / DURATION_OF_BOOST));
        const barColorForTimer = determinedPlayerColor; 
        const timerBarWidthBase = Math.min(200, BASE_CANVAS_WIDTH - 20); // Make timer bar fit narrow screen
        const timerBarHeightBase = 10;
        // Timer bar X position adjusted
        const timerBarXBase = (BASE_CANVAS_WIDTH - timerBarWidthBase) / 2; 
        const timerBarYBase = 10; // Y position at top
        ctx.fillStyle = 'rgba(100, 100, 100, 0.5)'; 
        ctx.fillRect(timerBarXBase*canvasScale, timerBarYBase*canvasScale, timerBarWidthBase*canvasScale, timerBarHeightBase*canvasScale);
        ctx.fillStyle = barColorForTimer;
        ctx.fillRect(timerBarXBase*canvasScale, timerBarYBase*canvasScale, (timerBarWidthBase * fillRatio)*canvasScale, timerBarHeightBase*canvasScale);
      }
      ctx.fillStyle='var(--red-main-ff0000)'; 
      ctx.font=`${20*canvasScale}px VT323`; 
      ctx.textAlign='left'; 
      // Score and Level at top-left
      ctx.fillText(`Score: ${score}`,10*canvasScale,30*canvasScale);
      ctx.fillText(`Level: ${level}`,10*canvasScale,55*canvasScale);
      
      // Health bar at top-right or centered at top
      const hbw=Math.min(200, BASE_CANVAS_WIDTH - 20); // Health bar width adjusted
      const hbh=20;
      const hbx=(BASE_CANVAS_WIDTH - hbw) / 2; // Centered health bar at top
      const hby=30 + 25 + 10; // Below Score/Level
      // const hbx=BASE_CANVAS_WIDTH - hbw - 10; // Health bar at top-right
      // const hby=30; 

      ctx.fillStyle='#330000';ctx.fillRect(hbx*canvasScale,hby*canvasScale,hbw*canvasScale,hbh*canvasScale);
      ctx.fillStyle='var(--red-main-ff0000)';ctx.fillRect(hbx*canvasScale,hby*canvasScale,(p.health/p.maxHealth)*hbw*canvasScale,hbh*canvasScale);
      ctx.fillStyle='#ffffff';ctx.font=`${14*canvasScale}px VT323`;ctx.textAlign='center'; 
      ctx.fillText(`Health: ${p.health}`,(hbx+hbw/2)*canvasScale,(hby+hbh*0.75)*canvasScale);
    }

    if(!isMobile) renderCRTOverlay();
  }, [score, level, enemiesKilled, renderCRTOverlay, isMobile, gameStateRef.current.player.health]); 

  const gameLoop = useCallback((currentTime: number) => {
    if (gameState === 'playing' && !isPaused) {
      updateGame(currentTime);
      render();
    }
    animationRef.current = requestAnimationFrame(gameLoop); 
  }, [gameState, isPaused, updateGame, render]);

  const handleSubmitScore = useCallback(async () => {
    if (!blueskyHandle.trim()) { setShowConfirmAnonymous(true); return; }
    setIsVerifying(true); setVerificationError('');
    try {
      const res = await verifyBlueskyHandle({ handle: blueskyHandle });
      if (res.valid && res.did) {
        const id = await saveScore({ playerName: blueskyHandle, blueskyHandle, blueskyDid: res.did, score, level, enemiesKilled });
        setSavedScoreId(id); setGameState('gameOver');
      } else setVerificationError(res.error || 'Invalid handle');
    } catch (e) { setVerificationError('Failed to verify handle'); } 
    finally { setIsVerifying(false); }
  }, [blueskyHandle, score, level, enemiesKilled, saveScore, verifyBlueskyHandle]);

  const handleAnonymousSubmit = useCallback(async () => {
    const id = await saveScore({ playerName: 'Anonymous', score, level, enemiesKilled });
    setSavedScoreId(id); setGameState('gameOver'); setShowConfirmAnonymous(false);
  }, [score, level, enemiesKilled, saveScore]);

  const startGame = useCallback(() => {
    setGameState('playing'); setScore(0); setLevel(1); setEnemiesKilled(0);
    setBlueskyHandle(''); setVerificationError(''); setShowConfirmAnonymous(false);
    setIsPaused(false); 
    gameStateRef.current = {
      player: { 
        // Player initial position adjusted for vertical mode
        x:BASE_CANVAS_WIDTH/2-25, y:BASE_CANVAS_HEIGHT-60, width:50, height:30, 
        health:100, maxHealth:100, fireRate:300, lastShot:0, 
        powerLevel:1, 
        activeMultiShotLevel: 0, 
        multiShotLevelExpireTime: 0,
        autoHealCharges: 0,
      },
      enemies:[],bullets:[],powerUps:[],particles:[],keys:{},lastEnemySpawn:0,enemySpawnRate:2000,lastTime:0,shakeIntensity:0,shakeDecay:0.9,
      isBossActiveRef: false,
      lastBossSpawnBlockRef: -1,
      globalBossPowerLevelRef: 2, 
      barrierLine: 6, 
      specialShip: null,
      crate: null,
      lastBarrierBoostSpawnLevelBlock: -1, 
      lastAutoHealSpawnLevelBlock: -1,    
    };
    playSound('backgroundMusic');
    if (animationRef.current) cancelAnimationFrame(animationRef.current); 
    animationRef.current = requestAnimationFrame(gameLoop);
  }, [gameLoop, playSound, setIsPaused]);

  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      if (isMobile) return;
      gameStateRef.current.keys[e.key] = true; if (e.key === ' ') e.preventDefault();
    };
    const onKeyUp = (e: KeyboardEvent) => { if (isMobile) return; gameStateRef.current.keys[e.key] = false; };
    window.addEventListener('keydown', onKeyDown); window.addEventListener('keyup', onKeyUp);
    return () => {
      window.removeEventListener('keydown', onKeyDown); window.removeEventListener('keyup', onKeyUp);
    };
  }, [isMobile, gameState]);

  useEffect(() => { 
    if (gameState === 'playing' && !animationRef.current) { 
        animationRef.current = requestAnimationFrame(gameLoop);
    } else if (gameState !== 'playing' && animationRef.current) { 
        cancelAnimationFrame(animationRef.current);
        animationRef.current = null;
    }
    return () => { 
        if (animationRef.current) {
            cancelAnimationFrame(animationRef.current);
            animationRef.current = null;
        }
    };
  }, [gameState, gameLoop]);

  useEffect(() => {
    if (initialDevState === 'playing' && gameState === 'playing') {
      console.log('[DevUtils] initialDevState is "playing", calling startGame() to ensure full initialization...');
      startGame();
    }
  }, [initialDevState, gameState, startGame, isMaximized]); 

  const handlePlayerMove = useCallback((direction: 'left' | 'right' | 'stop') => {
    if (!isMobile || gameState !== 'playing' || isPaused) return;
    gameStateRef.current.keys['touchLeft'] = direction === 'left';
    gameStateRef.current.keys['touchRight'] = direction === 'right';
  }, [isMobile, gameState, isPaused]);

  const handleCanvasToggleMaximized = useCallback(() => {
    setIsMaximized(!isMaximized); 
  }, [isMaximized, setIsMaximized]);

  // UI components (ScoreSubmissionScreen, OptionsScreen, etc.) are assumed to be flexible enough
  // or will need separate styling/layout adjustments for portrait mode later.
  // For now, they will render as is.
  if (gameState === 'submitScore') return <ScoreSubmissionScreen {...{blueskyHandle,setBlueskyHandle,isVerifying,verificationError,handleSubmitScore,showConfirmAnonymous,setShowConfirmAnonymous,handleAnonymousSubmit}} />;
  // OptionsScreen might need adjustment for narrow layout if it's too wide.
  if (gameState === 'options') return <OptionsScreen {...{settings,setSettings,setGameState, isMaximized, onToggleMaximized: handleCanvasToggleMaximized}} />;
  if (gameState === 'leaderboard') return <LeaderboardScreen setGameState={setGameState} />;
  if (gameState === 'menu') return <MenuScreen startGame={startGame} setGameState={setGameState} />;
  if (gameState === 'gameOver') return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-black text-red-500" style={{ fontFamily: 'VT323, monospace' }}>
      <GameOverScreen {...{score,level,enemiesKilled,savedScoreId,startGame,setGameState}} />
      {savedScoreId && blueskyHandle && <div className="mt-8"><BlueskyShare {...{scoreId:savedScoreId,blueskyHandle,score,onClose:()=>{setSavedScoreId(null);setGameState('menu');}}}/></div>}
    </div>
  );

  // Main game layout, uses CSS variables for canvas dimensions
  return (
    <div className={`flex flex-col min-h-screen ${isMobile ? 'items-around justify-center' : 'items-center justify-center'}`} style={{ fontFamily: 'VT323, monospace' }}>
      <div 
        className={`flex items-center gap-2 relative ${isMobile ? 'flex-col h-full' : ''}`}
        style={{
          ['--canvas-width' as string]: `${canvasDimsForStyle.width}px`,
          ['--canvas-height' as string]: `${canvasDimsForStyle.height}px`,
        }}
      >
          {/* ControlsDisplay might be too wide for portrait, hide or adjust later */}
          {!isMobile && <ControlsDisplay {...{keyAnimationRef,settings,getMovementKeys, keyMap: gameKeyMap}} />}
        <div className={`flex flex-col items-center relative ${isMobile ? 'h-full' : ''}`}> 
          {isMobile && gameState === 'playing' && (
            <MobileStatsDisplay 
              score={score}
              level={level}
              enemiesKilled={enemiesKilled}
              playerHealth={gameStateRef.current.player.health}
            />
          )}
          {/* PowerUpLegend might need layout adjustment for portrait */}
          {!isMobile && <PowerUpLegend autoHealCharges={gameStateRef.current.player.autoHealCharges} />}
          <GameScreen 
            canvasRef={canvasRef} 
            overlayCanvasRef={overlayCanvasRef}
            isMobile={isMobile}
            isMaximized={isMaximized} 
          />
        
          {gameState === 'playing' && <PauseButton onClick={togglePause} />} 
        </div>
        
        {isMobile && gameState === 'playing' && (
          <MobileCanvas onPlayerMove={handlePlayerMove} />
        )}

        <PauseMenu 
          isPaused={isPaused && gameState === 'playing'} 
          onResume={togglePause} 
          onSettings={() => { setIsPaused(false); setGameState('options'); }} 
          onQuit={() => { 
            setIsPaused(false); 
            setGameState('menu'); 
            if (audioRefs.current.backgroundMusic) audioRefs.current.backgroundMusic.pause();
          }} 
        />
      </div>
    </div>
  );
}

