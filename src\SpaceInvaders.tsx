import React, { useRef, useEffect, useState, useCallback } from "react";
import { useMutation, useAction, useQuery } from "convex/react"; // Added useQuery
import { api } from "../convex/_generated/api";
import { Id } from "../convex/_generated/dataModel";
import {
  checkPlayerLevelUp,
  getPlayerHealthBoostOnLevelUp,
  getEnemySpawnCount,
  INACTIVITY_CONSTANTS, // <-- Import new constants
  getInactivityState, // <-- Import new function
} from "./gameLogic/difficultyManager";
import BlueskyShare from "./BlueskyShare";
import {
  PowerUpLegend,
  ControlsDisplay,
  ScoreSubmissionScreen,
  OptionsScreen,
  MenuScreen,
  GameOverScreen,
  GameScreen,
  MobileStatsDisplay,
} from "./components/gameUI/gameUi";
import GameHUD from "./components/gameUI/GameHUD";
import { drawPlayerShip } from "./components/gameUI/playerShip";
import {
  use<PERSON><PERSON>eGame,
  <PERSON>useMenu,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useGameControls,
} from "./components/gameUI/features";
import { LeaderboardScreen } from "./components/gameUI/leaderboards";
import MobileCanvas from "./components/gameUI/mobileCanvas";
import { UpgradeScreen } from "./components/gameUI/UpgradeScreen";
import { UpgradeManager } from "./gameLogic/upgrades/upgradeManager";
import { Upgrade as UpgradeDefinition } from "./gameLogic/upgrades/definitions";
import {
  Asteroid,
  manageAsteroidSpawning,
  updateAsteroids,
  handleAsteroidCollisions,
} from "./gameLogic/asteroidManager";
import { drawAsteroid } from "./components/gameUI/asteroidRenderer";
import { createGameLogicHooks, GameLogicDependencies } from "./SpaceInvadersGameLogic";

// Helper function to format game duration
const formatGameDuration = (ms: number): string => {
  if (ms < 0) ms = 0;
  const totalSeconds = Math.floor(ms / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  if (minutes > 0) {
    return `${minutes}m ${seconds.toString().padStart(2, "0")}s`;
  }
  return `${seconds}s`;
};

// Define all game object interfaces at the top level
export interface GameObject {
  x: number;
  y: number;
  width: number;
  height: number;
  vx?: number;
  vy?: number;
}

export interface Player extends GameObject {
  health: number;
  maxHealth: number;
  fireRate: number;
  lastShot: number;
  powerLevel: number;
  activeMultiShotLevel: number;
  multiShotLevelExpireTime: number;
  autoHealCharges: number;
  lastSignificantMoveX: number; // For anti-camping
  timeStationaryMs: number; // For anti-camping
  isBlinkingRed?: boolean; // For anti-camping visual cue
  lastPenaltyAppliedAtSecondTier: number; // For anti-camping penalty logic
  invincibilityEndTime: number; // For Sol Invictus upgrade
  killStreak: number;
}

interface Enemy extends GameObject {
  type: "basic" | "fast" | "heavy" | "boss";
  health: number;
  maxHealth: number;
  points: number;
  lastShot: number;
  fireRate: number;
  powerLevel?: number;
  attackPattern?: "multiSpread" | "focusedBarrage";
  nextPatternSwitchTime?: number;
  isTelegraphing?: boolean;
  telegraphCompleteTime?: number;
  telegraphColor?: string;
}

export interface Bullet extends GameObject {
  damage: number;
  isPlayerBullet: boolean;
}

interface PowerUp extends GameObject {
  type: "health" | "fireRate" | "multiShot" | "shield";
  duration?: number;
}

export interface Particle extends GameObject {
  life: number;
  maxLife: number;
  color: string;
}

interface SpecialShip extends GameObject {
  type: "barrierBoost" | "autoHeal";
  health: number;
  dropDelayTimer: number;
  hasDroppedCrate: boolean;
  vx: number;
}

interface Crate extends GameObject {
  type: "barrierBoost" | "autoHeal";
  spawnTime: number;
  vy: number;
}

interface FloatingText {
  text: string;
  x: number;
  y: number;
  life: number;
  maxLife: number;
  color: string;
  isStreakBonus?: boolean;
}

interface Explosion {
  x: number;
  y: number;
  radius: number;
  maxRadius: number;
  life: number;
  maxLife: number;
  color: string;
}

interface GameInternalState {
  player: Player;
  enemies: Enemy[];
  bullets: Bullet[];
  powerUps: PowerUp[];
  particles: Particle[];
  floatingTexts: FloatingText[];
  explosions: Explosion[];
  asteroids: Asteroid[];
  keys: Record<string, boolean>;
  lastEnemySpawn: number;
  lastAsteroidSpawn: number;
  lastSoloAsteroidSpawn: number;
  enemySpawnRate: number;
  lastTime: number;
  shakeIntensity: number;
  shakeDecay: number;
  isBossActiveRef: boolean;
  lastBossSpawnBlockRef: number;
  globalBossPowerLevelRef: number;
  barrierLine: number;
  specialShip: SpecialShip | null;
  crate: Crate | null;
  lastBarrierBoostSpawnLevelBlock: number;
  lastAutoHealSpawnLevelBlock: number;
  totalRunTimeMs: number; // For run timer UI
  gameSpeed: number; // For time dilation
  timeDilationEndTime: number; // For time dilation
  isMaxStreakActive: boolean;
}

export interface GameSettings {
  enemySpawnRate: number;
  playerFireRate: number;
  enemyBulletSpeed: number;
  playerBulletSpeed: number;
  musicVolume: number;
  sfxVolume: number;
  musicEnabled: boolean;
  sfxEnabled: boolean;
  controlScheme: "arrows" | "wasd";
}

export type GameState =
  | "menu"
  | "playing"
  | "gameOver"
  | "options"
  | "submitScore"
  | "leaderboard"
  | "upgrading";

interface SpaceInvadersProps {
  initialDevState?: GameState;
  onGameStateChange?: (newState: GameState) => void;
  isMaximized: boolean;
  setIsMaximized: (isMax: boolean) => void;
}

let CANVAS_WIDTH = 507;
let CANVAS_HEIGHT = 900;
const PLAYER_SPEED = 8;
const BASE_CANVAS_WIDTH = 507;
const BASE_CANVAS_HEIGHT = 900;
let canvasScale = 1;

const DEFAULT_SETTINGS: GameSettings = {
  enemySpawnRate: 1.0,
  playerFireRate: 1.0,
  enemyBulletSpeed: 1.0,
  playerBulletSpeed: 1.0,
  musicVolume: 0.5,
  sfxVolume: 0.7,
  musicEnabled: true,
  sfxEnabled: true,
  controlScheme: "arrows",
};

export default function SpaceInvaders({
  initialDevState,
  onGameStateChange,
  isMaximized,
  setIsMaximized,
}: SpaceInvadersProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const overlayCanvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);
  const backgroundAnimationRef = useRef<number>(0);
  const crtAnimationRef = useRef<number>(0);
  const keyAnimationRef = useRef<{
    left: number;
    right: number;
    space: number;
  }>({ left: 0, right: 0, space: 0 });
  const upgradeManagerRef = useRef(new UpgradeManager());
  const [upgradeOptions, setUpgradeOptions] = useState<UpgradeDefinition[]>([]);

  const [gameState, _setGameState] = useState<GameState>(
    initialDevState || "menu"
  );

  const [canvasDimsForStyle, setCanvasDimsForStyle] = useState({
    width: BASE_CANVAS_WIDTH,
    height: BASE_CANVAS_HEIGHT,
  });

  const setGameState = useCallback(
    (newState: GameState | ((prevState: GameState) => GameState)) => {
      _setGameState((prevState) => {
        const resolvedState =
          typeof newState === "function" ? newState(prevState) : newState;
        if (onGameStateChange) {
          onGameStateChange(resolvedState);
        }
        return resolvedState;
      });
    },
    [onGameStateChange]
  );

  useEffect(() => {
    if (onGameStateChange) {
      onGameStateChange(gameState);
    }
  }, [gameState, onGameStateChange]);

  const [score, setScore] = useState(0);
  const [level, setLevel] = useState(1);
  const [enemiesKilled, setEnemiesKilled] = useState(0);
  const [settings, setSettings] = useState<GameSettings>(DEFAULT_SETTINGS);
  const [blueskyHandle, setBlueskyHandle] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationError, setVerificationError] = useState("");
  const [showConfirmAnonymous, setShowConfirmAnonymous] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [showTopRankConsent, setShowTopRankConsent] = useState(false);
  const [isMaxStreakActive, setIsMaxStreakActive] = useState(false);
  const [borderColor, setBorderColor] = useState("transparent");

  const audioRefs = useRef<{
    playerShoot: HTMLAudioElement | null;
    playerHit: HTMLAudioElement | null;
    enemyHit: HTMLAudioElement | null;
    enemyKill: HTMLAudioElement | null;
    powerupPickup: HTMLAudioElement | null;
    backgroundMusic: HTMLAudioElement | null;
  }>({
    playerShoot: null,
    playerHit: null,
    enemyHit: null,
    enemyKill: null,
    powerupPickup: null,
    backgroundMusic: null,
  });

  const { isPaused, togglePause, setIsPaused } = usePauseGame(
    gameState,
    audioRefs,
    settings,
    // Reset the game's internal timer when unpausing to prevent anti-camping penalties
    () => {
      gameStateRef.current.lastTime = performance.now();
    }
  );

  const [savedScoreId, setSavedScoreId] = useState<Id<"scores"> | null>(null);
  const [leaderboardPosition, setLeaderboardPosition] = useState<
    number | string | null
  >(null); // For BlueskyShare
  const submitScoreAndCheckRank = useAction(api.index.submitScoreAndCheckRank);
  const postTopRankAnnouncement = useAction(api.social.postTopRankAnnouncement);
  const verifyBlueskyHandle = useAction(api.social.verifyBlueskyHandle);

  // Fetch rank when savedScoreId is available
  const fetchedRank = useQuery(
    api.index.getScoreRank,
    savedScoreId ? { scoreId: savedScoreId } : "skip"
  );

  useEffect(() => {
    if (fetchedRank !== undefined && fetchedRank !== null) {
      setLeaderboardPosition(fetchedRank);
    } else if (savedScoreId && fetchedRank === null) {
      // Score submitted but rank not found (edge case) or still loading
      setLeaderboardPosition("N/A"); // Or some loading indicator
    }
  }, [fetchedRank, savedScoreId]);

  const gameStateRef = useRef<GameInternalState>({
    player: {
      x: BASE_CANVAS_WIDTH / 2 - 25,
      y: BASE_CANVAS_HEIGHT - 60,
      width: 50,
      height: 30,
      health: 100,
      maxHealth: 100,
      fireRate: 300,
      lastShot: 0,
      powerLevel: 1,
      activeMultiShotLevel: 0,
      multiShotLevelExpireTime: 0,
      autoHealCharges: 0,
      lastSignificantMoveX: BASE_CANVAS_WIDTH / 2 - 25,
      timeStationaryMs: 0,
      isBlinkingRed: false,
      lastPenaltyAppliedAtSecondTier:
        INACTIVITY_CONSTANTS.PENALTY_START_THRESHOLD_S - 1,
      invincibilityEndTime: 0,
    } as Player,
    enemies: [] as Enemy[],
    bullets: [] as Bullet[],
    powerUps: [] as PowerUp[],
    particles: [] as Particle[],
    floatingTexts: [] as FloatingText[],
    explosions: [] as Explosion[],
    asteroids: [] as Asteroid[],
    keys: {} as Record<string, boolean>,
    lastEnemySpawn: 0,
    lastAsteroidSpawn: 0,
    lastSoloAsteroidSpawn: 0,
    enemySpawnRate: 2000,
    lastTime: 0,
    shakeIntensity: 0,
    shakeDecay: 0.9,
    isBossActiveRef: false,
    lastBossSpawnBlockRef: -1,
    globalBossPowerLevelRef: 2,
    barrierLine: 6,
    specialShip: null,
    crate: null,
    lastBarrierBoostSpawnLevelBlock: -1,
    lastAutoHealSpawnLevelBlock: -1,
    totalRunTimeMs: 0,
    gameSpeed: 1.0,
    timeDilationEndTime: 0,
    isMaxStreakActive: false,
  });

  useEffect(() => {
    const updateCanvasDimensions = () => {
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;

      const newEffectivelyMobile = screenWidth < 768 && !isMaximized;
      if (newEffectivelyMobile !== isMobile) {
        setIsMobile(newEffectivelyMobile);
      }

      let newWidth: number;
      let newHeight: number;

      if (isMaximized) {
        const aspectRatio = BASE_CANVAS_WIDTH / BASE_CANVAS_HEIGHT;
        if (screenWidth / screenHeight > aspectRatio) {
          newHeight = screenHeight;
          newWidth = newHeight * aspectRatio;
        } else {
          newWidth = screenWidth;
          newHeight = newWidth / aspectRatio;
        }
      } else if (newEffectivelyMobile) {
        const aspectRatio = BASE_CANVAS_WIDTH / BASE_CANVAS_HEIGHT;
        newWidth = screenWidth;
        newHeight = screenWidth / aspectRatio;
      } else {
        newWidth = BASE_CANVAS_WIDTH;
        newHeight = BASE_CANVAS_HEIGHT;
      }

      CANVAS_WIDTH = newWidth;
      CANVAS_HEIGHT = newHeight;
      canvasScale = CANVAS_WIDTH / BASE_CANVAS_WIDTH;

      setCanvasDimsForStyle((prevDims) => {
        if (prevDims.width === newWidth && prevDims.height === newHeight) {
          return prevDims;
        }
        return { width: newWidth, height: newHeight };
      });

      const canvasStyle = {
        width: `${newWidth}px`,
        height: `${newHeight}px`,
        position: "" as const,
        top: "",
        left: "",
        zIndex: "",
      };

      if (canvasRef.current) {
        canvasRef.current.width = newWidth;
        canvasRef.current.height = newHeight;
        Object.assign(canvasRef.current.style, canvasStyle);
      }
      if (overlayCanvasRef.current) {
        overlayCanvasRef.current.width = newWidth;
        overlayCanvasRef.current.height = newHeight;
        Object.assign(overlayCanvasRef.current.style, canvasStyle);
      }
    };

    updateCanvasDimensions();
    window.addEventListener("resize", updateCanvasDimensions);

    if (isMaximized || gameState === "playing") {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }

    return () => {
      window.removeEventListener("resize", updateCanvasDimensions);
      if (document.body && !isMaximized && gameState !== "playing") {
        document.body.style.overflow = "";
      } else if (document.body) {
        document.body.style.overflow = "hidden";
      }
    };
  }, [gameState, isMaximized, isMobile]);

  useEffect(() => {
    const loadAudio = (filename: string) => {
      const audio = new Audio(`/audio/${filename}`);
      audio.preload = "auto";
      audio.addEventListener("error", () =>
        console.warn(`Could not load audio file: ${filename}`)
      );
      return audio;
    };

    audioRefs.current = {
      playerShoot: loadAudio("player-shoot.mp3"),
      playerHit: loadAudio("player-hit.mp3"),
      enemyHit: loadAudio("enemy-hit.mp3"),
      enemyKill: loadAudio("enemy-kill.mp3"),
      powerupPickup: loadAudio("powerup-pickup.mp3"),
      backgroundMusic: loadAudio("background-music.mp3"),
    };

    if (audioRefs.current.backgroundMusic) {
      audioRefs.current.backgroundMusic.loop = true;
    }
  }, []);

  useEffect(() => {
    const animateKeys = () => {
      if (Math.random() < 0.01) keyAnimationRef.current.left = Date.now();
      if (Math.random() < 0.01) keyAnimationRef.current.right = Date.now();
      if (Math.random() < 0.005) keyAnimationRef.current.space = Date.now();
    };
    const interval = setInterval(animateKeys, 100);
    return () => clearInterval(interval);
  }, []);











  const { getMovementKeys, keyMap: gameKeyMap } = useGameControls(settings);

  // Create game logic dependencies
  const gameLogicDependencies: GameLogicDependencies = {
    gameStateRef,
    audioRefs,
    upgradeManagerRef,
    backgroundAnimationRef,
    crtAnimationRef,
    settings,
    level,
    enemiesKilled,
    isMobile,
    setScore,
    setLevel,
    setEnemiesKilled,
    setGameState,
    setUpgradeOptions,
    setIsMaxStreakActive,
    getMovementKeys,
  };

  // Get the extracted game logic functions
  const gameLogicHooks = createGameLogicHooks(gameLogicDependencies);
  const { playSound, createParticles, checkCollision, createEnemy, spawnEnemies, spawnSpecialBonuses, updateGame } = gameLogicHooks;

  const updateGame = useCallback(
    (currentTime: number) => {
      const state = gameStateRef.current;
      const dt = currentTime - state.lastTime;
      if (dt <= 0) {
        return;
      }
      state.lastTime = currentTime;

      // Time Dilation Logic
      const DILATION_DURATION = 5000; // 5 seconds
      if (currentTime < state.timeDilationEndTime) {
        const timePassed =
          DILATION_DURATION - (state.timeDilationEndTime - currentTime);
        const progress = timePassed / DILATION_DURATION;
        state.gameSpeed = 0.1 + 0.9 * progress; // Ramp from 0.1 to 1.0
      } else {
        state.gameSpeed = 1.0;
      }

      // Adjust audio playback rate based on game speed
      Object.values(audioRefs.current).forEach((audio) => {
        if (audio) {
          audio.playbackRate = state.gameSpeed;
        }
      });

      const scaledDt = dt * state.gameSpeed;

      state.totalRunTimeMs += scaledDt;
      backgroundAnimationRef.current += scaledDt * 0.001;
      crtAnimationRef.current += scaledDt * 0.01;

      const player = state.player;
      const playerXBeforeMove = player.x;
      const moveKeys = getMovementKeys();

      if (
        player.activeMultiShotLevel > 0 &&
        currentTime > player.multiShotLevelExpireTime
      ) {
        player.activeMultiShotLevel--;
        if (player.activeMultiShotLevel > 0) {
          player.multiShotLevelExpireTime = currentTime + 10000;
        } else {
          player.multiShotLevelExpireTime = 0;
        }
      }
      player.powerLevel = 1 + player.activeMultiShotLevel;

      const effectivePlayerSpeed = PLAYER_SPEED * state.gameSpeed;

      const fireBullet = () => {
        const bulletSpreadLevel =
          upgradeManagerRef.current.getUpgradeLevel("bulletSpread");
        const shouldSpread = Math.random() < bulletSpreadLevel / 100;

        if (shouldSpread) {
          for (let i = 0; i < player.powerLevel; i++) {
            const offX =
              player.powerLevel > 1
                ? (i - (player.powerLevel - 1) / 2) * 35
                : 0;
            const spawnX = player.x + player.width / 2 - 2 + offX;
            state.bullets.push({
              x: spawnX,
              y: player.y,
              width: 4,
              height: 10,
              vy: -12 * settings.playerBulletSpeed,
              damage: 1,
              isPlayerBullet: true,
            });
            state.bullets.push({
              x: spawnX,
              y: player.y,
              width: 4,
              height: 10,
              vx: -4,
              vy: -11 * settings.playerBulletSpeed,
              damage: 1,
              isPlayerBullet: true,
            });
            state.bullets.push({
              x: spawnX,
              y: player.y,
              width: 4,
              height: 10,
              vx: 4,
              vy: -11 * settings.playerBulletSpeed,
              damage: 1,
              isPlayerBullet: true,
            });
          }
        } else {
          for (let i = 0; i < player.powerLevel; i++) {
            const offX =
              player.powerLevel > 1
                ? (i - (player.powerLevel - 1) / 2) * 35
                : 0;
            state.bullets.push({
              x: player.x + player.width / 2 - 2 + offX,
              y: player.y,
              width: 4,
              height: 10,
              vy: -12 * settings.playerBulletSpeed,
              damage: 1,
              isPlayerBullet: true,
            });
          }
        }
        player.lastShot = currentTime;
        playSound("playerShoot");
      };

      if (isMobile) {
        if (state.keys["touchLeft"] && player.x > 0)
          player.x -= effectivePlayerSpeed;
        if (
          state.keys["touchRight"] &&
          player.x < BASE_CANVAS_WIDTH - player.width
        )
          player.x += effectivePlayerSpeed;
        if (
          currentTime - player.lastShot >
          player.fireRate / settings.playerFireRate / state.gameSpeed
        ) {
          fireBullet();
        }
      } else {
        if (state.keys[moveKeys.left] && player.x > 0)
          player.x -= effectivePlayerSpeed;
        if (
          state.keys[moveKeys.right] &&
          player.x < BASE_CANVAS_WIDTH - player.width
        )
          player.x += effectivePlayerSpeed;
        if (
          state.keys[moveKeys.shoot] &&
          currentTime - player.lastShot >
            player.fireRate / settings.playerFireRate / state.gameSpeed
        ) {
          fireBullet();
        }
      }

      const playerMovedHorizontallyThisFrame = player.x !== playerXBeforeMove;
      if (
        playerMovedHorizontallyThisFrame &&
        Math.abs(player.x - player.lastSignificantMoveX) >=
          INACTIVITY_CONSTANTS.SIGNIFICANT_MOVE_PX
      ) {
        player.timeStationaryMs = 0;
        player.isBlinkingRed = false;
        player.lastSignificantMoveX = player.x;
        player.lastPenaltyAppliedAtSecondTier =
          INACTIVITY_CONSTANTS.PENALTY_START_THRESHOLD_S - 1;
      } else if (!playerMovedHorizontallyThisFrame) {
        player.timeStationaryMs += scaledDt;
      }

      const inactivityResult = getInactivityState(
        player.timeStationaryMs,
        player.lastPenaltyAppliedAtSecondTier
      );
      player.isBlinkingRed = inactivityResult.shouldBlink;
      if (inactivityResult.healthPenalty > 0) {
        player.health -= inactivityResult.healthPenalty;
        player.lastPenaltyAppliedAtSecondTier =
          inactivityResult.newLastAppliedPenaltyTier;
        createParticles(
          player.x + player.width / 2,
          player.y + player.height / 2,
          5,
          "#cc0000"
        );
        playSound("playerHit");
        state.shakeIntensity = 3;
      }

      if (!isMobile && state.keys["h"]) {
        if (player.autoHealCharges > 0 && player.health < player.maxHealth) {
          player.health = player.maxHealth;
          player.autoHealCharges--;
          playSound("powerupPickup");
          createParticles(
            player.x + player.width / 2,
            player.y + player.height / 2,
            15,
            "#88ff88"
          );
          state.keys["h"] = false;
        } else {
          state.keys["h"] = false;
        }
      }

      spawnEnemies(currentTime);
      spawnSpecialBonuses(currentTime);

      const spawnResult = manageAsteroidSpawning(
        currentTime,
        state.lastAsteroidSpawn,
        state.lastSoloAsteroidSpawn,
        BASE_CANVAS_WIDTH
      );
      if (spawnResult.newAsteroids.length > 0) {
        state.asteroids.push(...spawnResult.newAsteroids);
      }
      state.lastAsteroidSpawn = spawnResult.newLastWaveSpawnTime;
      state.lastSoloAsteroidSpawn = spawnResult.newLastSoloSpawnTime;

      updateAsteroids(state.asteroids, state.gameSpeed, currentTime);

      if (state.specialShip) {
        state.specialShip.x += state.specialShip.vx * state.gameSpeed;
        if (
          state.specialShip.vx > 0 &&
          state.specialShip.x > BASE_CANVAS_WIDTH
        ) {
          state.specialShip = null;
        } else if (
          state.specialShip.vx < 0 &&
          state.specialShip.x + state.specialShip.width < 0
        ) {
          state.specialShip = null;
        } else if (
          state.specialShip &&
          state.specialShip.dropDelayTimer > 0 &&
          currentTime >= state.specialShip.dropDelayTimer &&
          !state.specialShip.hasDroppedCrate
        ) {
          if (!state.crate) {
            const CRATE_WIDTH = 20;
            const CRATE_HEIGHT = 20;
            const CRATE_FALL_SPEED = 2;
            const newCrate: Crate = {
              x:
                state.specialShip.x +
                state.specialShip.width / 2 -
                CRATE_WIDTH / 2,
              y:
                state.specialShip.y +
                state.specialShip.height / 2 -
                CRATE_HEIGHT / 2,
              width: CRATE_WIDTH,
              height: CRATE_HEIGHT,
              vy: CRATE_FALL_SPEED,
              type: state.specialShip.type,
              spawnTime: currentTime,
            };
            state.crate = newCrate;
            playSound("powerupPickup");
          }
          state.specialShip.hasDroppedCrate = true;
          state.specialShip = null;
        }
      }

      if (state.crate) {
        state.crate.y += state.crate.vy * state.gameSpeed;
        if (checkCollision(state.player, state.crate)) {
          if (state.crate.type === "barrierBoost") {
            state.barrierLine = Math.max(0, state.barrierLine - 5);
          } else if (state.crate.type === "autoHeal") {
            state.player.autoHealCharges = Math.min(
              1,
              (state.player.autoHealCharges || 0) + 1
            );
          }
          createParticles(
            state.crate.x + state.crate.width / 2,
            state.crate.y + state.crate.height / 2,
            15,
            "#ffff88"
          );
          playSound("powerupPickup");
          state.crate = null;
        } else if (state.crate.y > BASE_CANVAS_HEIGHT) {
          state.crate = null;
        }
      }

      const BARRIER_LINE_HEIGHT_DIVISOR = 12;
      const barrierPixelY =
        state.barrierLine * (BASE_CANVAS_HEIGHT / BARRIER_LINE_HEIGHT_DIVISOR);

      state.enemies.forEach((e: Enemy) => {
        const intendedY = e.y + (e.vy || 0) * state.gameSpeed;
        if (
          e.vy &&
          e.vy > 0 &&
          e.y + e.height < barrierPixelY &&
          intendedY + e.height >= barrierPixelY
        ) {
          e.y = barrierPixelY - e.height;
          e.vy = 0;
        } else if (e.y + e.height >= barrierPixelY) {
          e.y = barrierPixelY - e.height;
          if (e.vy && e.vy > 0) e.vy = 0;
        } else {
          e.y = intendedY;
        }
        e.x += (e.vx || 0) * state.gameSpeed;
        if (e.x <= 0 || e.x >= BASE_CANVAS_WIDTH - e.width) e.vx = -(e.vx || 0);
        if (e.type === "boss") {
          const TELEGRAPH_DURATION = 1000;
          const PATTERN_SWITCH_INTERVAL = 8000;
          if (
            e.attackPattern === undefined ||
            e.nextPatternSwitchTime === undefined ||
            e.isTelegraphing === undefined ||
            e.telegraphCompleteTime === undefined
          ) {
            e.attackPattern = "multiSpread";
            e.nextPatternSwitchTime = currentTime + PATTERN_SWITCH_INTERVAL;
            e.isTelegraphing = false;
            e.telegraphCompleteTime = 0;
            e.telegraphColor = undefined;
          }
          if (currentTime > e.nextPatternSwitchTime && !e.isTelegraphing) {
            e.attackPattern =
              e.attackPattern === "multiSpread"
                ? "focusedBarrage"
                : "multiSpread";
            e.nextPatternSwitchTime = currentTime + PATTERN_SWITCH_INTERVAL;
            e.isTelegraphing = true;
            e.telegraphCompleteTime = currentTime + TELEGRAPH_DURATION;
            e.telegraphColor =
              e.attackPattern === "multiSpread"
                ? "rgba(255, 100, 100, 0.7)"
                : "rgba(100, 100, 255, 0.7)";
          }
          if (currentTime - e.lastShot > e.fireRate / state.gameSpeed) {
            let canFireNow = false;
            if (e.isTelegraphing) {
              if (currentTime > e.telegraphCompleteTime) {
                e.isTelegraphing = false;
                e.telegraphColor = undefined;
                canFireNow = true;
              }
            } else {
              canFireNow = true;
            }
            if (canFireNow && e.powerLevel && e.powerLevel > 0) {
              if (e.attackPattern === "multiSpread") {
                for (let i = 0; i < e.powerLevel; i++) {
                  const offX =
                    e.powerLevel > 1 ? (i - (e.powerLevel - 1) / 2) * 20 : 0;
                  state.bullets.push({
                    x: e.x + e.width / 2 - 2 + offX,
                    y: e.y + e.height,
                    width: 4,
                    height: 10,
                    vy: 5 * settings.enemyBulletSpeed,
                    damage: 15,
                    isPlayerBullet: false,
                  });
                }
              } else if (e.attackPattern === "focusedBarrage") {
                for (let i = 0; i < e.powerLevel; i++) {
                  const offX = (i - (e.powerLevel - 1) / 2) * 5;
                  state.bullets.push({
                    x: e.x + e.width / 2 - 2 + offX,
                    y: e.y + e.height,
                    width: 3,
                    height: 12,
                    vy: 7 * settings.enemyBulletSpeed,
                    damage: 12,
                    isPlayerBullet: false,
                  });
                }
              }
              e.lastShot = currentTime;
            }
          }
        } else {
          if (currentTime - e.lastShot > e.fireRate / state.gameSpeed) {
            let attemptShot = false;
            let fireDiagonally = false;
            const baseBulletSpeed = 6 * settings.enemyBulletSpeed;

            const enemyIdForLog = `${e.type}_${e.x.toFixed(0)}_${e.y.toFixed(0)}`;

            if (level >= 21 && (e.type === "fast" || e.type === "heavy")) {
              const SPECIAL_ENEMY_SHOOT_PROBABILITY = 0.5;
              if (Math.random() < SPECIAL_ENEMY_SHOOT_PROBABILITY) {
                attemptShot = true;
                const DIAGONAL_CHANCE = 0.15;
                if (Math.random() < DIAGONAL_CHANCE) {
                  console.log(
                    `[DIAG_DEBUG L${level}] Eligible enemy ${enemyIdForLog} PASSED 15% DIAGONAL CHANCE. FIRING DIAGONAL!`
                  );
                  fireDiagonally = true;
                }
              }
            } else {
              const STANDARD_ENEMY_SHOOT_PROBABILITY = 0.005;
              if (Math.random() < STANDARD_ENEMY_SHOOT_PROBABILITY) {
                attemptShot = true;
              }
            }

            if (attemptShot) {
              let bulletVx = 0;
              let bulletVy = baseBulletSpeed;

              if (fireDiagonally) {
                const angleRad = 30 * (Math.PI / 180);
                bulletVy = baseBulletSpeed * Math.cos(angleRad);
                const horizontalSpeedComponent =
                  baseBulletSpeed * Math.sin(angleRad);
                bulletVx =
                  Math.random() < 0.5
                    ? -horizontalSpeedComponent
                    : horizontalSpeedComponent;
                console.log(
                  `[DIAG_DEBUG L${level}] Pushing DIAGONAL bullet for ${enemyIdForLog}: vx=${bulletVx.toFixed(2)}, vy=${bulletVy.toFixed(2)}`
                );
              }

              state.bullets.push({
                x: e.x + e.width / 2 - 2,
                y: e.y + e.height,
                width: 4,
                height: 8,
                vx: bulletVx,
                vy: bulletVy,
                damage: 10,
                isPlayerBullet: false,
              });
              e.lastShot = currentTime;
            }
          }
        }
      });

      state.bullets.forEach((b: Bullet) => {
        b.x += (b.vx || 0) * state.gameSpeed;
        b.y += (b.vy || 0) * state.gameSpeed;
      });
      state.particles.forEach((p: Particle) => {
        p.x += (p.vx || 0) * state.gameSpeed;
        p.y += (p.vy || 0) * state.gameSpeed;
        p.life -= state.gameSpeed;
      });
      state.floatingTexts.forEach((ft) => {
        ft.y -= 0.5 * state.gameSpeed;
        ft.life -= state.gameSpeed;
      });
      state.explosions.forEach((exp) => {
        exp.life -= scaledDt;
        exp.radius = exp.maxRadius * (1 - exp.life / exp.maxLife);
      });

      state.bullets.forEach((bullet: Bullet, bIdx: number) => {
        if (bullet.isPlayerBullet) {
          let bulletRemoved = false;
          state.enemies.forEach((enemy: Enemy, eIdx: number) => {
            if (bulletRemoved) return;

            if (checkCollision(bullet, enemy)) {
              bulletRemoved = true;
              state.bullets.splice(bIdx, 1);

              // Explosive Johnny Logic
              const explosiveJohnnyLevel =
                upgradeManagerRef.current.getUpgradeLevel("explosiveJohnny");
              if (
                explosiveJohnnyLevel > 0 &&
                Math.random() < explosiveJohnnyLevel * 0.005
              ) {
                const explosionRadius = 50;
                const explosionDamage = bullet.damage * 0.25;

                state.explosions.push({
                  x: bullet.x,
                  y: bullet.y,
                  radius: 0,
                  maxRadius: explosionRadius,
                  life: 300,
                  maxLife: 300,
                  color: "rgba(255, 140, 0, 0.8)",
                });

                playSound("enemyKill");

                state.enemies.forEach((otherEnemy) => {
                  if (otherEnemy !== enemy) {
                    const dx =
                      otherEnemy.x +
                      otherEnemy.width / 2 -
                      (enemy.x + enemy.width / 2);
                    const dy =
                      otherEnemy.y +
                      otherEnemy.height / 2 -
                      (enemy.y + enemy.height / 2);
                    if (Math.sqrt(dx * dx + dy * dy) < explosionRadius) {
                      otherEnemy.health -= explosionDamage;
                      createParticles(
                        otherEnemy.x + otherEnemy.width / 2,
                        otherEnemy.y + otherEnemy.height / 2,
                        5,
                        "#ffcc00"
                      );
                    }
                  }
                });
              }

              enemy.health -= bullet.damage;
              createParticles(
                enemy.x + enemy.width / 2,
                enemy.y + enemy.height / 2,
                8,
                "var(--red-secondary-ff4444)"
              );
              playSound("enemyHit");

              const vampireGiftLevel =
                upgradeManagerRef.current.getUpgradeLevel("vampireGift");
              if (
                vampireGiftLevel > 0 &&
                Math.random() < vampireGiftLevel * 0.001
              ) {
                player.health = Math.min(player.maxHealth, player.health + 10);
                state.floatingTexts.push({
                  text: "+10",
                  x: player.x + player.width / 2,
                  y: player.y,
                  life: 60,
                  maxLife: 60,
                  color: "#00ff00",
                });
              }

              if (enemy.health <= 0) {
                const greedIsGoodLevel =
                  upgradeManagerRef.current.getUpgradeLevel("greedIsGood");
                const scoreMultiplier = 1 + greedIsGoodLevel * 0.02;

                // Streak Logic
                player.killStreak++;
                const streakMilestones = [10, 25, 50];
                let streakBonus = 0;
                let milestoneReached = -1;

                for (let i = streakMilestones.length - 1; i >= 0; i--) {
                  if (player.killStreak >= streakMilestones[i]) {
                    streakBonus = streakMilestones[i];
                    if (player.killStreak === streakMilestones[i]) {
                      milestoneReached = streakMilestones[i];
                    }
                    break;
                  }
                }

                if (player.killStreak >= 50) {
                  state.isMaxStreakActive = true;
                }

                if (milestoneReached !== -1) {
                  state.floatingTexts.push({
                    text: `+${milestoneReached}`,
                    x: BASE_CANVAS_WIDTH / 2,
                    y: BASE_CANVAS_HEIGHT / 2,
                    life: 60,
                    maxLife: 60,
                    color: "#ffff007d",
                    isStreakBonus: true,
                  });
                }

                setScore(
                  (s) =>
                    s + Math.floor(enemy.points * scoreMultiplier) + streakBonus
                );

                setEnemiesKilled((ek) => ek + 1);
                if (enemy.type === "boss") {
                  state.isBossActiveRef = false;
                  if (state.barrierLine < 11) state.barrierLine++;
                }
                state.enemies.splice(eIdx, 1);
                createParticles(
                  enemy.x + enemy.width / 2,
                  enemy.y + enemy.height / 2,
                  15,
                  "var(--red-main-ff0000)"
                );
                state.shakeIntensity = 5;
                playSound("enemyKill");
                if (Math.random() < 0.15) {
                  const types: PowerUp["type"][] = [
                    "health",
                    "fireRate",
                    "multiShot",
                  ];
                  state.powerUps.push({
                    x: enemy.x,
                    y: enemy.y,
                    width: 20,
                    height: 20,
                    vy: 2,
                    type: types[Math.floor(Math.random() * types.length)],
                  });
                }
              }
            }
          });
          if (
            !bulletRemoved &&
            state.specialShip &&
            !state.specialShip.hasDroppedCrate &&
            state.specialShip.dropDelayTimer === 0 &&
            checkCollision(bullet, state.specialShip)
          ) {
            state.specialShip.health -= bullet.damage;
            state.bullets.splice(bIdx, 1);
            createParticles(
              state.specialShip.x + state.specialShip.width / 2,
              state.specialShip.y + state.specialShip.height / 2,
              8,
              "#88ddff"
            );
            playSound("enemyHit");
            if (state.specialShip.health <= 0) {
              state.specialShip.dropDelayTimer = currentTime + 500;
            }
          }
        } else if (checkCollision(bullet, player)) {
          if (currentTime > player.invincibilityEndTime) {
            player.health -= bullet.damage;
            player.killStreak = 0; // Reset streak
            state.isMaxStreakActive = false; // Reset max streak flag
            playSound("playerHit");
            state.shakeIntensity = 8;
            createParticles(
              player.x + player.width / 2,
              player.y + player.height / 2,
              5,
              "#ff6666"
            );

            const solInvictusLevel =
              upgradeManagerRef.current.getUpgradeLevel("solInvictus");
            if (solInvictusLevel > 0) {
              player.invincibilityEndTime =
                currentTime + solInvictusLevel * 250;
            }
          } else {
            createParticles(
              player.x + player.width / 2,
              player.y + player.height / 2,
              3,
              "#ffff00"
            );
          }
          state.bullets.splice(bIdx, 1);
        }
      });

      state.powerUps.forEach((pUp: PowerUp, idx: number) => {
        pUp.y += (pUp.vy || 0) * state.gameSpeed;
        if (checkCollision(pUp, player)) {
          if (pUp.type === "health")
            player.health = Math.min(player.maxHealth, player.health + 30);
          else if (pUp.type === "fireRate")
            player.fireRate = Math.max(50, player.fireRate - 30);
          else if (pUp.type === "multiShot") {
            player.activeMultiShotLevel = Math.min(
              5,
              player.activeMultiShotLevel + 1
            );
            player.multiShotLevelExpireTime = currentTime + 10000;
          }
          setScore((s) => s + 5);
          state.powerUps.splice(idx, 1);
          createParticles(pUp.x + 10, pUp.y + 10, 10, "#ff8888");
          playSound("powerupPickup");
        }
      });

      const collisionResults = handleAsteroidCollisions(
        state.asteroids,
        player,
        state.bullets,
        createParticles,
        playSound as (sound: string) => void,
        currentTime
      );
      if (collisionResults.playerHealthLost > 0) {
        if (currentTime > player.invincibilityEndTime) {
          player.health -= collisionResults.playerHealthLost;
          player.killStreak = 0; // Reset streak
          state.isMaxStreakActive = false; // Reset max streak flag
          playSound("playerHit");
          state.shakeIntensity = 10;
          createParticles(
            player.x + player.width / 2,
            player.y + player.height / 2,
            20,
            "#ff4444"
          );

          const solInvictusLevel =
            upgradeManagerRef.current.getUpgradeLevel("solInvictus");
          if (solInvictusLevel > 0) {
            player.invincibilityEndTime = currentTime + solInvictusLevel * 250;
          }
        }
      }
      if (collisionResults.bulletsToRemove.size > 0) {
        state.bullets = state.bullets.filter(
          (b) => !collisionResults.bulletsToRemove.has(b)
        );
      }

      state.bullets = state.bullets.filter(
        (b: Bullet) => b.y > -20 && b.y < BASE_CANVAS_HEIGHT + 20
      );
      state.enemies = state.enemies.filter(
        (e: Enemy) => e.y < BASE_CANVAS_HEIGHT + 50
      );
      state.powerUps = state.powerUps.filter(
        (p: PowerUp) => p.y < BASE_CANVAS_HEIGHT
      );
      state.asteroids = state.asteroids.filter(
        (a: Asteroid) => a.y < BASE_CANVAS_HEIGHT + a.height
      );
      state.particles = state.particles.filter((p: Particle) => p.life > 0);
      state.floatingTexts = state.floatingTexts.filter((ft) => ft.life > 0);
      state.explosions = state.explosions.filter((exp) => exp.life > 0);
      state.shakeIntensity *= state.shakeDecay;

      if (checkPlayerLevelUp(enemiesKilled, level)) {
        const newLevel = level + 1;
        setLevel(newLevel);
        player.health = Math.min(
          player.maxHealth,
          player.health + getPlayerHealthBoostOnLevelUp()
        );

        if (newLevel > 1 && newLevel % 5 === 0) {
          setUpgradeOptions(upgradeManagerRef.current.getUpgradeOptions());
          setGameState("upgrading");
        }
      }

      if (player.health <= 0) {
        const isGodMode =
          import.meta.env.DEV && localStorage.getItem("devGodMode") === "true";
        if (isGodMode) {
          player.health = player.maxHealth;
          console.log("[DevUtils] God Mode: Player health restored!");
          createParticles(
            player.x + player.width / 2,
            player.y + player.height / 2,
            20,
            "#00ff00"
          );
        } else {
          setGameState("submitScore");
          if (audioRefs.current.backgroundMusic)
            audioRefs.current.backgroundMusic.pause();
        }
      }
    },
    [
      checkCollision,
      createParticles,
      spawnEnemies,
      enemiesKilled,
      level,
      settings,
      playSound,
      getMovementKeys,
      isMobile,
    ]
  );

  const renderCRTOverlay = useCallback(() => {
    const overlay = overlayCanvasRef.current;
    if (!overlay || isMobile) return;
    const ctx = overlay.getContext("2d");
    if (!ctx) return;
    ctx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    const time = crtAnimationRef.current;
    const grad = ctx.createRadialGradient(
      CANVAS_WIDTH / 2,
      CANVAS_HEIGHT / 2,
      0,
      CANVAS_WIDTH / 2,
      CANVAS_HEIGHT / 2,
      Math.max(CANVAS_WIDTH, CANVAS_HEIGHT) * 0.8
    );
    grad.addColorStop(0, "rgba(0,0,0,0)");
    grad.addColorStop(0.7, "rgba(0,0,0,0.1)");
    grad.addColorStop(1, "rgba(0,0,0,0.6)");
    ctx.fillStyle = grad;
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    ctx.fillStyle = "rgba(0,0,0,0.15)";
    for (let i = 0; i < CANVAS_HEIGHT; i += 3)
      ctx.fillRect(0, i, CANVAS_WIDTH, 1);
    const scanY = (time * 2) % CANVAS_HEIGHT;
    ctx.fillStyle = "rgba(255,255,255,0.05)";
    ctx.fillRect(0, scanY, CANVAS_WIDTH, 2);
    ctx.globalCompositeOperation = "screen";
    ctx.fillStyle = "rgba(255,0,0,0.02)";
    ctx.fillRect(1, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    ctx.fillStyle = "rgba(0,255,0,0.02)";
    ctx.fillRect(-1, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    ctx.fillStyle = "rgba(0,0,255,0.02)";
    ctx.fillRect(0, 1, CANVAS_WIDTH, CANVAS_HEIGHT);
    ctx.globalCompositeOperation = "source-over";
    if (Math.random() < 0.01) {
      ctx.fillStyle = `rgba(255,255,255,${Math.random() * 0.1})`;
      ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    }
  }, [isMobile]);

  const render = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;
    const state = gameStateRef.current;

    ctx.save();
    if (state.shakeIntensity > 0.1)
      ctx.translate(
        (Math.random() - 0.5) * state.shakeIntensity,
        (Math.random() - 0.5) * state.shakeIntensity
      );

    const grad = ctx.createLinearGradient(0, 0, 0, CANVAS_HEIGHT);
    const t = backgroundAnimationRef.current;
    const w1 = Math.sin(t * 0.5) * 0.1 + 0.1;
    const w2 = Math.sin(t * 0.3) * 0.05 + 0.05;
    grad.addColorStop(0, `rgba(${Math.floor(10 + w1 * 20)},0,0,1)`);
    grad.addColorStop(0.3, `rgba(${Math.floor(20 + w2 * 30)},0,0,0.8)`);
    grad.addColorStop(0.7, `rgba(${Math.floor(15 + w1 * 25)},0,0,0.6)`);
    grad.addColorStop(1, `rgba(${Math.floor(5 + w2 * 15)},0,0,1)`);
    ctx.fillStyle = grad;
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

    ctx.strokeStyle = "rgba(255,0,0,0.1)";
    ctx.lineWidth = 1 * canvasScale;
    for (let i = 0; i < 10; i++) {
      const yb = (t * 20 + i * 60) % (BASE_CANVAS_HEIGHT + 60);
      const ys = yb * canvasScale;
      ctx.beginPath();
      ctx.moveTo(0, ys);
      ctx.lineTo(CANVAS_WIDTH, ys);
      ctx.stroke();
    }

    const BARRIER_LINE_HEIGHT_DIVISOR = 12;
    const BARRIER_COLOR = "#00FFFF";
    const BARRIER_THICKNESS = 3;
    const barrierYPosition =
      state.barrierLine * (BASE_CANVAS_HEIGHT / BARRIER_LINE_HEIGHT_DIVISOR);

    ctx.fillStyle = BARRIER_COLOR;
    ctx.shadowColor = BARRIER_COLOR;
    ctx.shadowBlur = 5 * canvasScale;
    ctx.fillRect(
      0,
      barrierYPosition * canvasScale - (BARRIER_THICKNESS * canvasScale) / 2,
      CANVAS_WIDTH,
      BARRIER_THICKNESS * canvasScale
    );
    ctx.shadowBlur = 0;

    const p = state.player;
    let determinedPlayerColor = "#ff0000";
    let determinedPlayerInnerColor = "#ff4444";

    if (p.powerLevel === 2) {
      determinedPlayerColor = "#0000ff";
      determinedPlayerInnerColor = "#4444ff";
    } else if (p.powerLevel === 3) {
      determinedPlayerColor = "#00ff00";
      determinedPlayerInnerColor = "#44ff44";
    } else if (p.powerLevel === 4) {
      determinedPlayerColor = "#ffff00";
      determinedPlayerInnerColor = "#ffff44";
    } else if (p.powerLevel >= 5) {
      determinedPlayerColor = "#ff00ff";
      determinedPlayerInnerColor = "#ff44ff";
    }

    const isInvincible = state.lastTime < p.invincibilityEndTime;
    if (isInvincible) {
      if (Math.floor(state.lastTime / 100) % 2 === 0) {
        determinedPlayerColor = "#ffff00";
        determinedPlayerInnerColor = "#ffffcc";
      } else {
        determinedPlayerColor = "#ffffff";
        determinedPlayerInnerColor = "#dddddd";
      }
    }
    drawPlayerShip(
      ctx,
      p,
      determinedPlayerColor,
      determinedPlayerInnerColor,
      canvasScale,
      p.isBlinkingRed,
      state.lastTime
    );

    state.enemies.forEach((e: Enemy) => {
      let c = "#ff3333";
      if (e.type === "fast") c = "#ff6666";
      else if (e.type === "heavy") c = "#cc0000";
      else if (e.type === "boss") c = "#990000";
      ctx.fillStyle = c;
      ctx.shadowColor = c;
      ctx.shadowBlur = 8 * canvasScale;
      ctx.fillRect(
        e.x * canvasScale,
        e.y * canvasScale,
        e.width * canvasScale,
        e.height * canvasScale
      );
      if (e.type === "boss" && e.isTelegraphing && e.telegraphColor) {
        ctx.save();
        ctx.fillStyle = e.telegraphColor;
        const telegraphProgress =
          e.telegraphCompleteTime && e.telegraphCompleteTime > state.lastTime
            ? 1 - (e.telegraphCompleteTime - state.lastTime) / 1000
            : 1;
        const alpha = 0.3 + Math.sin(telegraphProgress * Math.PI * 4) * 0.2;
        const baseColorMatch = e.telegraphColor.match(
          /rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/
        );
        if (baseColorMatch) {
          ctx.fillStyle = `rgba(${baseColorMatch[1]}, ${baseColorMatch[2]}, ${baseColorMatch[3]}, ${Math.max(0.1, Math.min(0.5, alpha))})`;
        } else {
          ctx.fillStyle = e.telegraphColor;
        }
        ctx.fillRect(
          e.x * canvasScale,
          e.y * canvasScale,
          e.width * canvasScale,
          e.height * canvasScale
        );
        ctx.restore();
      }
      if (e.health < e.maxHealth) {
        ctx.fillStyle = "#330000";
        ctx.fillRect(
          e.x * canvasScale,
          (e.y - 8) * canvasScale,
          e.width * canvasScale,
          4 * canvasScale
        );
        ctx.fillStyle = "var(--red-main-ff0000)";
        ctx.fillRect(
          e.x * canvasScale,
          (e.y - 8) * canvasScale,
          (e.health / e.maxHealth) * e.width * canvasScale,
          4 * canvasScale
        );
      }
    });

    if (state.specialShip) {
      let shipColor = "#88ddff";
      if (
        state.specialShip.dropDelayTimer > 0 &&
        !state.specialShip.hasDroppedCrate
      ) {
        if (Math.floor(state.lastTime / 150) % 2 === 0) {
          shipColor = "#ffffff";
        }
      }
      ctx.fillStyle = shipColor;
      ctx.shadowColor = shipColor;
      ctx.shadowBlur = 8 * canvasScale;
      ctx.fillRect(
        state.specialShip.x * canvasScale,
        state.specialShip.y * canvasScale,
        state.specialShip.width * canvasScale,
        state.specialShip.height * canvasScale
      );
      ctx.shadowBlur = 0;
    }

    if (state.crate) {
      let crateColor = "#dddddd";
      let crateSymbol = "?";
      if (state.crate.type === "barrierBoost") {
        crateColor = "#00FFFF";
        crateSymbol = "B";
      } else if (state.crate.type === "autoHeal") {
        crateColor = "#88ff88";
        crateSymbol = "H";
      }
      ctx.fillStyle = crateColor;
      ctx.shadowColor = crateColor;
      ctx.shadowBlur = 8 * canvasScale;
      ctx.fillRect(
        state.crate.x * canvasScale,
        state.crate.y * canvasScale,
        state.crate.width * canvasScale,
        state.crate.height * canvasScale
      );
      ctx.shadowBlur = 0;
      ctx.fillStyle = "#000000";
      ctx.font = `${14 * canvasScale}px VT323`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillText(
        crateSymbol,
        (state.crate.x + state.crate.width / 2) * canvasScale,
        (state.crate.y + state.crate.height / 2) * canvasScale
      );
      ctx.textBaseline = "alphabetic";
    }

    state.asteroids.forEach((asteroid: Asteroid) => {
      drawAsteroid(ctx, asteroid, canvasScale, state.lastTime);
    });

    state.bullets.forEach((b: Bullet) => {
      ctx.shadowColor = b.isPlayerBullet ? "#e2e595" : "#ff4444";
      ctx.shadowBlur = 6 * canvasScale;
      ctx.fillStyle = b.isPlayerBullet ? "#e2e595" : "#ff4444";
      ctx.fillRect(
        b.x * canvasScale,
        b.y * canvasScale,
        b.width * canvasScale,
        b.height * canvasScale
      );
      ctx.shadowBlur = 0;
    });
    state.powerUps.forEach((pu: PowerUp) => {
      ctx.shadowColor = "#ff8888";
      ctx.shadowBlur = 8 * canvasScale;
      ctx.fillStyle = "#ff8888";
      ctx.fillRect(
        pu.x * canvasScale,
        pu.y * canvasScale,
        pu.width * canvasScale,
        pu.height * canvasScale
      );
      ctx.shadowBlur = 0;
      ctx.fillStyle = "#ffffff";
      ctx.font = `${26 * canvasScale}px VT323`;
      ctx.textAlign = "center";
      const i =
        pu.type === "health"
          ? "+"
          : pu.type === "fireRate"
            ? "F"
            : pu.type === "multiShot"
              ? "M"
              : "S";
      ctx.fillText(i, (pu.x + 10) * canvasScale, (pu.y + 14) * canvasScale);
    });
    state.particles.forEach((pt: Particle) => {
      const a = pt.life / pt.maxLife;
      ctx.fillStyle =
        pt.color +
        Math.floor(a * 255)
          .toString(16)
          .padStart(2, "0");
      ctx.fillRect(
        pt.x * canvasScale,
        pt.y * canvasScale,
        pt.width * canvasScale,
        pt.height * canvasScale
      );
    });
    state.floatingTexts.forEach((ft) => {
      if (ft.isStreakBonus) {
        const progress = 1 - ft.life / ft.maxLife;
        let scale;
        let opacity;

        if (progress < 0.5) {
          // Fade in and scale up
          scale = progress * 2; // 0 -> 1
          opacity = progress * 2 * 0.3; // 0 -> 0.5
        } else {
          // Fade out and scale down
          scale = (1 - progress) * 2; // 1 -> 0
          opacity = (1 - progress) * 2 * 0.2; // 0.5 -> 0
        }

        const maxFontSize = CANVAS_WIDTH * 0.2;
        const currentFontSize = maxFontSize * scale;

        ctx.fillStyle = `rgba(255, 255, 0, ${opacity})`;
        ctx.font = `bold ${currentFontSize}px VT323`;
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";
        ctx.fillText(ft.text, ft.x * canvasScale, ft.y * canvasScale);
        ctx.textBaseline = "alphabetic";
      } else {
        const alpha = ft.life / ft.maxLife;
        if (Math.floor(ft.life / 5) % 2 === 0) {
          ctx.fillStyle = `rgba(0, 255, 0, ${alpha})`;
          ctx.font = `bold ${20 * canvasScale}px VT323`;
          ctx.textAlign = "center";
          ctx.fillText(ft.text, ft.x * canvasScale, ft.y * canvasScale);
        }
      }
    });

    state.explosions.forEach((exp) => {
      ctx.beginPath();
      ctx.arc(
        exp.x * canvasScale,
        exp.y * canvasScale,
        exp.radius * canvasScale,
        0,
        Math.PI * 2
      );
      ctx.strokeStyle = exp.color;
      ctx.lineWidth = 2 * canvasScale;
      ctx.stroke();
    });

    ctx.restore();

    if (!isMobile) {
      if (p.activeMultiShotLevel > 0) {
        const DURATION_OF_BOOST = 10000;
        const timeRemaining = p.multiShotLevelExpireTime - state.lastTime;
        const fillRatio = Math.max(
          0,
          Math.min(1, timeRemaining / DURATION_OF_BOOST)
        );
        const barColorForTimer = determinedPlayerColor;
        const timerBarWidthBase = 200;
        const timerBarHeightBase = 10;
        const timerBarXBase = (BASE_CANVAS_WIDTH - timerBarWidthBase) / 2;
        const timerBarYBase = 10;
        ctx.fillStyle = "rgba(100, 100, 100, 0.5)";
        ctx.fillRect(
          timerBarXBase * canvasScale,
          timerBarYBase * canvasScale,
          timerBarWidthBase * canvasScale,
          timerBarHeightBase * canvasScale
        );
        ctx.fillStyle = barColorForTimer;
        ctx.fillRect(
          timerBarXBase * canvasScale,
          timerBarYBase * canvasScale,
          timerBarWidthBase * fillRatio * canvasScale,
          timerBarHeightBase * canvasScale
        );
      }

      const newHbhBase = 20;
      const newHbh = newHbhBase * canvasScale;
      const newHbw = CANVAS_WIDTH * 0.95;
      const newHbx = (CANVAS_WIDTH - newHbw) / 2;

      const healthBarYOffset = 25 * canvasScale;
      const playerBottomY = (p.y + p.height) * canvasScale;
      let newHby = playerBottomY + healthBarYOffset;

      const minMarginFromBottom = 5 * canvasScale;
      if (newHby + newHbh > CANVAS_HEIGHT - minMarginFromBottom) {
        newHby = CANVAS_HEIGHT - newHbh - minMarginFromBottom;
      }
      if (newHby < playerBottomY + 5 * canvasScale) {
        newHby = playerBottomY + 5 * canvasScale;
      }

      ctx.fillStyle = "#330000";
      ctx.fillRect(newHbx, newHby, newHbw, newHbh);

      const healthRatio = Math.max(0, p.health) / p.maxHealth;
      ctx.fillStyle = "#ff0000";
      ctx.fillRect(newHbx, newHby, newHbw * healthRatio, newHbh);

      ctx.fillStyle = "#ffffff";
      const healthBarTextSize = Math.max(12 * canvasScale, 16);
      ctx.font = `bold ${healthBarTextSize}px VT323`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillText(
        `Health: ${Math.max(0, p.health)}`,
        newHbx + newHbw / 2,
        newHby + newHbh / 2
      );

      ctx.textAlign = "left";
      ctx.textBaseline = "alphabetic";

      if (p.isBlinkingRed && state.lastTime) {
        if (Math.floor(state.lastTime / 150) % 2 === 0) {
          const fontSize = CANVAS_WIDTH * 0.2;
          ctx.font = `bold ${fontSize}px VT323`;
          ctx.fillStyle = "rgba(255, 0, 0, 0.3)";
          ctx.textAlign = "center";
          ctx.textBaseline = "middle";
          ctx.fillText("MOVE", CANVAS_WIDTH / 2, CANVAS_HEIGHT / 2);
          ctx.textAlign = "left";
          ctx.textBaseline = "alphabetic";
        }
      }
    }

    if (!isMobile) renderCRTOverlay();
  }, [
    score,
    level,
    enemiesKilled,
    renderCRTOverlay,
    isMobile,
    gameStateRef.current.player.health,
  ]);

  const gameLoop = useCallback(
    (currentTime: number) => {
      if (gameState === "playing" && !isPaused) {
        updateGame(currentTime);
        render();
      }
      if (gameStateRef.current.isMaxStreakActive !== isMaxStreakActive) {
        setIsMaxStreakActive(gameStateRef.current.isMaxStreakActive);
      }
      animationRef.current = requestAnimationFrame(gameLoop);
    },
    [gameState, isPaused, updateGame, render, isMaxStreakActive]
  );

  const handleScoreSubmission = useCallback(
    async (submissionArgs: any) => {
      try {
        const result = await submitScoreAndCheckRank(submissionArgs);
        if (result && result.scoreId) {
          setSavedScoreId(result.scoreId);
          setLeaderboardPosition(result.rank);

          if (result.rank && result.rank >= 1 && result.rank <= 3) {
            setShowTopRankConsent(true);
          } else {
            setGameState("gameOver");
          }
        }
      } catch (e) {
        console.error("Failed to submit score and check rank:", e);
        setVerificationError("Failed to submit score.");
      } finally {
        setIsVerifying(false);
      }
    },
    [submitScoreAndCheckRank]
  );

  const handleSubmitScore = useCallback(async () => {
    if (!blueskyHandle.trim()) {
      setShowConfirmAnonymous(true);
      return;
    }
    setIsVerifying(true);
    setVerificationError("");
    try {
      const res = await verifyBlueskyHandle({ handle: blueskyHandle });
      if (res.valid && res.did) {
        await handleScoreSubmission({
          playerName: blueskyHandle,
          blueskyHandle,
          blueskyDid: res.did,
          score,
          level,
          enemiesKilled,
          gameDurationMs: gameStateRef.current.totalRunTimeMs,
          isAnonymous: false,
        });
      } else {
        setVerificationError(res.error || "Invalid handle");
        setIsVerifying(false);
      }
    } catch (e) {
      setVerificationError("Failed to verify handle");
      setIsVerifying(false);
    }
  }, [
    blueskyHandle,
    score,
    level,
    enemiesKilled,
    verifyBlueskyHandle,
    handleScoreSubmission,
  ]);

  const handleAnonymousSubmit = useCallback(async () => {
    setShowConfirmAnonymous(false);
    setIsVerifying(true);
    await handleScoreSubmission({
      playerName: "Anonymous",
      score,
      level,
      enemiesKilled,
      gameDurationMs: gameStateRef.current.totalRunTimeMs,
      isAnonymous: true,
    });
  }, [score, level, enemiesKilled, handleScoreSubmission]);

  const TopRankConsentDialog = () => (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[200]">
      <div className="bg-[var(--background-color-main)] border-2 border-[var(--red-main-ff0000)] p-8 rounded-lg text-center shadow-lg">
        <h2 className="text-2xl mb-4 text-white">
          Your score is{" "}
          <span className="text-[var(--red-main-ff0000)]">legendary!</span>
        </h2>
        <p className="mb-6 text-white">
          Sire, our community should be informed. Allow me please to post your
          victory
        </p>
        <div className="flex justify-center gap-4">
          <button
            className="auth-button bg-[var(--red-main-ff0000)] hover:bg-[var(--red-secondary-ff4444)] text-white font-bold py-2 px-4 rounded"
            onClick={() => {
              void (async () => {
                if (savedScoreId) {
                  await postTopRankAnnouncement({ scoreId: savedScoreId });
                }
                setShowTopRankConsent(false);
                setGameState("gameOver");
              })();
            }}
          >
            Yes
          </button>
          <button
            className="auth-button bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
            onClick={() => {
              setShowTopRankConsent(false);
              setGameState("gameOver");
            }}
          >
            No
          </button>
        </div>
      </div>
    </div>
  );

  const startGame = useCallback(() => {
    setGameState("playing");
    setScore(0);
    setLevel(1);
    setEnemiesKilled(0);
    setBlueskyHandle("");
    setVerificationError("");
    setShowConfirmAnonymous(false);
    setSavedScoreId(null);
    setLeaderboardPosition(null);
    setIsPaused(false);
    upgradeManagerRef.current = new UpgradeManager();
    gameStateRef.current = {
      player: {
        x: BASE_CANVAS_WIDTH / 2 - 25,
        y: BASE_CANVAS_HEIGHT - 60,
        width: 50,
        height: 30,
        health: 100,
        maxHealth: 100,
        fireRate: 300,
        lastShot: 0,
        powerLevel: 1,
        activeMultiShotLevel: 0,
        multiShotLevelExpireTime: 0,
        autoHealCharges: 0,
        lastSignificantMoveX: BASE_CANVAS_WIDTH / 2 - 25,
        timeStationaryMs: 0,
        isBlinkingRed: false,
        lastPenaltyAppliedAtSecondTier:
          INACTIVITY_CONSTANTS.PENALTY_START_THRESHOLD_S - 1,
        invincibilityEndTime: 0,
        killStreak: 0,
      },
      enemies: [],
      bullets: [],
      powerUps: [],
      particles: [],
      floatingTexts: [],
      explosions: [],
      asteroids: [],
      keys: {},
      lastEnemySpawn: 0,
      lastAsteroidSpawn: 0,
      lastSoloAsteroidSpawn: 0,
      enemySpawnRate: 2000,
      lastTime: performance.now(),
      shakeIntensity: 0,
      shakeDecay: 0.9,
      isBossActiveRef: false,
      lastBossSpawnBlockRef: -1,
      globalBossPowerLevelRef: 2,
      barrierLine: 6,
      specialShip: null,
      crate: null,
      lastBarrierBoostSpawnLevelBlock: -1,
      lastAutoHealSpawnLevelBlock: -1,
      totalRunTimeMs: 0,
      gameSpeed: 1.0,
      timeDilationEndTime: 0,
      isMaxStreakActive: false,
    };
    playSound("backgroundMusic");
    if (animationRef.current) cancelAnimationFrame(animationRef.current);
    animationRef.current = requestAnimationFrame(gameLoop);
  }, [gameLoop, playSound, setIsPaused]);

  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      if (isMobile) return;
      {
      }
      gameStateRef.current.keys[e.key] = true;
      if (e.key === " ") e.preventDefault();
    };
    const onKeyUp = (e: KeyboardEvent) => {
      if (isMobile) return;
      gameStateRef.current.keys[e.key] = false;
    };
    window.addEventListener("keydown", onKeyDown);
    window.addEventListener("keyup", onKeyUp);
    return () => {
      window.removeEventListener("keydown", onKeyDown);
      window.removeEventListener("keyup", onKeyUp);
    };
  }, [isMobile, gameState]);

  useEffect(() => {
    if (gameState === "playing" && !animationRef.current) {
      animationRef.current = requestAnimationFrame(gameLoop);
    } else if (gameState !== "playing" && animationRef.current) {
      cancelAnimationFrame(animationRef.current);
      animationRef.current = null;
    }
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = null;
      }
    };
  }, [gameState, gameLoop]);

  useEffect(() => {
    if (initialDevState === "playing" && gameState === "playing") {
      console.log(
        '[DevUtils] initialDevState is "playing", calling startGame() to ensure full initialization...'
      );
      startGame();
    }
  }, [initialDevState, gameState, startGame, isMaximized]);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (isMaxStreakActive) {
      setBorderColor("var(--red-main-ff0000)"); // Start with red
      interval = setInterval(() => {
        setBorderColor((prev) =>
          prev === "white" ? "var(--red-main-ff0000)" : "white"
        );
      }, 250);
    } else {
      setBorderColor("transparent");
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isMaxStreakActive]);

  const handlePlayerMove = useCallback(
    (position: number) => {
      if (!isMobile || gameState !== "playing" || isPaused) return;

      const player = gameStateRef.current.player;
      const oldX = player.x;

      // Convert percentage to actual position
      const targetX = (position / 100) * BASE_CANVAS_WIDTH - player.width / 2;
      player.x = Math.max(
        0,
        Math.min(targetX, BASE_CANVAS_WIDTH - player.width)
      );

      // Reset anti-camping timer when actual movement occurs
      if (Math.abs(player.x - oldX) > 0) {
        player.timeStationaryMs = 0;
        player.isBlinkingRed = false;
        if (
          Math.abs(player.x - player.lastSignificantMoveX) >=
          INACTIVITY_CONSTANTS.SIGNIFICANT_MOVE_PX
        ) {
          player.lastSignificantMoveX = player.x;
          player.lastPenaltyAppliedAtSecondTier =
            INACTIVITY_CONSTANTS.PENALTY_START_THRESHOLD_S - 1;
        }
      }
    },
    [isMobile, gameState, isPaused]
  );

  const handleCanvasToggleMaximized = useCallback(() => {
    setIsMaximized(!isMaximized);
  }, [isMaximized, setIsMaximized]);

  const handleUpgradeSelected = (upgrade: UpgradeDefinition) => {
    upgradeManagerRef.current.levelUp(upgrade.id);

    if (upgrade.id === "healthyBastard") {
      gameStateRef.current.player.maxHealth += 10;
      gameStateRef.current.player.health += 10;
    }

    // Trigger time dilation
    gameStateRef.current.timeDilationEndTime = performance.now() + 5000;

    // Reset lastTime to prevent a large dt after the upgrade screen, which could trigger the anti-camp system unfairly.
    gameStateRef.current.lastTime = performance.now();
    setGameState("playing");
  };

  if (gameState === "upgrading") {
    return (
      <UpgradeScreen
        upgrades={upgradeOptions}
        onSelect={handleUpgradeSelected}
        getUpgradeLevel={(id) => upgradeManagerRef.current.getUpgradeLevel(id)}
      />
    );
  }

  if (showTopRankConsent) return <TopRankConsentDialog />;
  if (gameState === "submitScore")
    return (
      <ScoreSubmissionScreen
        {...{
          blueskyHandle,
          setBlueskyHandle,
          isVerifying,
          verificationError,
          handleSubmitScore,
          showConfirmAnonymous,
          setShowConfirmAnonymous,
          handleAnonymousSubmit,
        }}
      />
    );
  if (gameState === "options")
    return (
      <OptionsScreen
        {...{
          settings,
          setSettings,
          setGameState,
          isMaximized,
          onToggleMaximized: handleCanvasToggleMaximized,
        }}
      />
    );
  if (gameState === "leaderboard")
    return <LeaderboardScreen setGameState={setGameState} />;
  if (gameState === "menu")
    return <MenuScreen startGame={startGame} setGameState={setGameState} />;
  if (gameState === "gameOver")
    return (
      <div
        className="flex flex-col items-center justify-center min-h-screen bg-black text-[var(--red-main-ff0000)]"
        style={{ fontFamily: "VT323, monospace" }}
      >
        <GameOverScreen
          {...{
            score,
            level,
            enemiesKilled,
            savedScoreId,
            startGame,
            setGameState,
          }}
        />
        {savedScoreId && blueskyHandle && (
          <div className="mt-8">
            <BlueskyShare
              scoreId={savedScoreId}
              blueskyHandle={blueskyHandle}
              score={score}
              playerLevel={level}
              enemiesKilled={enemiesKilled}
              leaderboardPosition={
                typeof leaderboardPosition === "number"
                  ? leaderboardPosition
                  : undefined
              }
              gameDuration={formatGameDuration(
                gameStateRef.current.totalRunTimeMs
              )}
              onClose={() => {
                setSavedScoreId(null);
                setLeaderboardPosition(null);
                setGameState("menu");
              }}
            />
          </div>
        )}
      </div>
    );

  return (
    <div
      className={`flex flex-col min-h-screen ${isMobile ? "items-around justify-start" : "items-center justify-center"}`}
      style={{ fontFamily: "VT323, monospace" }}
    >
      {!isMobile && (
        <div className="lg:max-w-38 flex-shrink-0">
          <PowerUpLegend
            autoHealCharges={gameStateRef.current.player.autoHealCharges}
          />
        </div>
      )}
      <div
        className={`flex items-center gap-2 z-10 relative ${isMobile ? "flex-col h-full top-0" : "flex-row"}`}
        style={{
          ["--canvas-width" as string]: `${canvasDimsForStyle.width}px`,
          ["--canvas-height" as string]: `${canvasDimsForStyle.height}px`,
        }}
      >
        <div
          className="relative flex-grow" // This container establishes the stacking context and allows it to grow
          style={{
            width: canvasDimsForStyle.width,
            height: canvasDimsForStyle.height,
            boxShadow: `0 0 10px 5px ${borderColor}`,
            transition: "box-shadow 0.2s linear",
          }}
        >
          <GameScreen
            canvasRef={canvasRef}
            overlayCanvasRef={overlayCanvasRef}
            isMobile={isMobile}
            isMaximized={isMaximized}
          />
          {gameState === "playing" && (
            <GameHUD
              score={score}
              level={level}
              enemiesKilled={enemiesKilled}
              gameTimeMs={gameStateRef.current.totalRunTimeMs}
              isMobile={isMobile}
              canvasWidth={canvasDimsForStyle.width}
              canvasHeight={canvasDimsForStyle.height}
              lastLevel={level > 1 ? level - 1 : 1} // Pass previous level for animation
            />
          )}
          {gameState === "playing" && <PauseButton onClick={togglePause} />}
        </div>

        {!isMobile && (
          <div
            id="if-max-hide"
            className={`w-32 flex-shrink-0 ${isMaximized ? "hidden" : ""}`}
          >
            <ControlsDisplay
              {...{
                keyAnimationRef,
                settings,
                getMovementKeys,
                keyMap: gameKeyMap,
              }}
            />
          </div>
        )}

        {isMobile && gameState === "playing" && (
          <MobileCanvas onPlayerMove={handlePlayerMove} />
        )}

        <PauseMenu
          isPaused={isPaused && gameState === "playing"}
          onResume={togglePause}
          onSettings={() => {
            setIsPaused(false);
            setGameState("options");
          }}
          onQuit={() => {
            setIsPaused(false);
            setGameState("menu");
            if (audioRefs.current.backgroundMusic)
              audioRefs.current.backgroundMusic.pause();
          }}
        />
      </div>
    </div>
  );
}
