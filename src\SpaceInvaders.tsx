import React, { useRef, useEffect, useState, useCallback } from "react";
import { useMutation, useAction, useQuery } from "convex/react"; // Added useQuery
import { api } from "../convex/_generated/api";
import { Id } from "../convex/_generated/dataModel";
import {
  checkPlayerLevelUp,
  getPlayerHealthBoostOnLevelUp,
  getEnemySpawnCount,
  INACTIVITY_CONSTANTS, // <-- Import new constants
  getInactivityState, // <-- Import new function
} from "./gameLogic/difficultyManager";
import BlueskyShare from "./BlueskyShare";
import {
  PowerUpLegend,
  ControlsDisplay,
  ScoreSubmissionScreen,
  OptionsScreen,
  MenuScreen,
  GameOverScreen,
  GameScreen,
  MobileStatsDisplay,
} from "./components/gameUI/gameUi";
import GameHUD from "./components/gameUI/GameHUD";
import { drawPlayerShip } from "./components/gameUI/playerShip";
import {
  use<PERSON><PERSON>eGame,
  <PERSON>useMenu,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  useGameControls,
} from "./components/gameUI/features";
import { LeaderboardScreen } from "./components/gameUI/leaderboards";
import MobileCanvas from "./components/gameUI/mobileCanvas";
import { UpgradeScreen } from "./components/gameUI/UpgradeScreen";
import { UpgradeManager } from "./gameLogic/upgrades/upgradeManager";
import { Upgrade as UpgradeDefinition } from "./gameLogic/upgrades/definitions";
import {
  Asteroid,
  manageAsteroidSpawning,
  updateAsteroids,
  handleAsteroidCollisions,
} from "./gameLogic/asteroidManager";
import { drawAsteroid } from "./components/gameUI/asteroidRenderer";
import { createGameLogicHooks, GameLogicDependencies } from "./SpaceInvadersGameLogic";
import { useRenderingHooks, RendererDependencies } from "./SpaceInvadersRenderer";

// Helper function to format game duration
const formatGameDuration = (ms: number): string => {
  if (ms < 0) ms = 0;
  const totalSeconds = Math.floor(ms / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  if (minutes > 0) {
    return `${minutes}m ${seconds.toString().padStart(2, "0")}s`;
  }
  return `${seconds}s`;
};

// Define all game object interfaces at the top level
export interface GameObject {
  x: number;
  y: number;
  width: number;
  height: number;
  vx?: number;
  vy?: number;
}

export interface Player extends GameObject {
  health: number;
  maxHealth: number;
  fireRate: number;
  lastShot: number;
  powerLevel: number;
  activeMultiShotLevel: number;
  multiShotLevelExpireTime: number;
  autoHealCharges: number;
  lastSignificantMoveX: number; // For anti-camping
  timeStationaryMs: number; // For anti-camping
  isBlinkingRed?: boolean; // For anti-camping visual cue
  lastPenaltyAppliedAtSecondTier: number; // For anti-camping penalty logic
  invincibilityEndTime: number; // For Sol Invictus upgrade
  killStreak: number;
}

interface Enemy extends GameObject {
  type: "basic" | "fast" | "heavy" | "boss";
  health: number;
  maxHealth: number;
  points: number;
  lastShot: number;
  fireRate: number;
  powerLevel?: number;
  attackPattern?: "multiSpread" | "focusedBarrage";
  nextPatternSwitchTime?: number;
  isTelegraphing?: boolean;
  telegraphCompleteTime?: number;
  telegraphColor?: string;
}

export interface Bullet extends GameObject {
  damage: number;
  isPlayerBullet: boolean;
}

interface PowerUp extends GameObject {
  type: "health" | "fireRate" | "multiShot" | "shield";
  duration?: number;
}

export interface Particle extends GameObject {
  life: number;
  maxLife: number;
  color: string;
}

interface SpecialShip extends GameObject {
  type: "barrierBoost" | "autoHeal";
  health: number;
  dropDelayTimer: number;
  hasDroppedCrate: boolean;
  vx: number;
}

interface Crate extends GameObject {
  type: "barrierBoost" | "autoHeal";
  spawnTime: number;
  vy: number;
}

interface FloatingText {
  text: string;
  x: number;
  y: number;
  life: number;
  maxLife: number;
  color: string;
  isStreakBonus?: boolean;
}

interface Explosion {
  x: number;
  y: number;
  radius: number;
  maxRadius: number;
  life: number;
  maxLife: number;
  color: string;
}

interface GameInternalState {
  player: Player;
  enemies: Enemy[];
  bullets: Bullet[];
  powerUps: PowerUp[];
  particles: Particle[];
  floatingTexts: FloatingText[];
  explosions: Explosion[];
  asteroids: Asteroid[];
  keys: Record<string, boolean>;
  lastEnemySpawn: number;
  lastAsteroidSpawn: number;
  lastSoloAsteroidSpawn: number;
  enemySpawnRate: number;
  lastTime: number;
  shakeIntensity: number;
  shakeDecay: number;
  isBossActiveRef: boolean;
  lastBossSpawnBlockRef: number;
  globalBossPowerLevelRef: number;
  barrierLine: number;
  specialShip: SpecialShip | null;
  crate: Crate | null;
  lastBarrierBoostSpawnLevelBlock: number;
  lastAutoHealSpawnLevelBlock: number;
  totalRunTimeMs: number; // For run timer UI
  gameSpeed: number; // For time dilation
  timeDilationEndTime: number; // For time dilation
  isMaxStreakActive: boolean;
}

export interface GameSettings {
  enemySpawnRate: number;
  playerFireRate: number;
  enemyBulletSpeed: number;
  playerBulletSpeed: number;
  musicVolume: number;
  sfxVolume: number;
  musicEnabled: boolean;
  sfxEnabled: boolean;
  controlScheme: "arrows" | "wasd";
}

export type GameState =
  | "menu"
  | "playing"
  | "gameOver"
  | "options"
  | "submitScore"
  | "leaderboard"
  | "upgrading";

interface SpaceInvadersProps {
  initialDevState?: GameState;
  onGameStateChange?: (newState: GameState) => void;
  isMaximized: boolean;
  setIsMaximized: (isMax: boolean) => void;
}

let CANVAS_WIDTH = 507;
let CANVAS_HEIGHT = 900;
const PLAYER_SPEED = 8;
const BASE_CANVAS_WIDTH = 507;
const BASE_CANVAS_HEIGHT = 900;
let canvasScale = 1;

const DEFAULT_SETTINGS: GameSettings = {
  enemySpawnRate: 1.0,
  playerFireRate: 1.0,
  enemyBulletSpeed: 1.0,
  playerBulletSpeed: 1.0,
  musicVolume: 0.5,
  sfxVolume: 0.7,
  musicEnabled: true,
  sfxEnabled: true,
  controlScheme: "arrows",
};

export default function SpaceInvaders({
  initialDevState,
  onGameStateChange,
  isMaximized,
  setIsMaximized,
}: SpaceInvadersProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const overlayCanvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);
  const backgroundAnimationRef = useRef<number>(0);
  const crtAnimationRef = useRef<number>(0);
  const keyAnimationRef = useRef<{
    left: number;
    right: number;
    space: number;
  }>({ left: 0, right: 0, space: 0 });
  const upgradeManagerRef = useRef(new UpgradeManager());
  const [upgradeOptions, setUpgradeOptions] = useState<UpgradeDefinition[]>([]);

  const [gameState, _setGameState] = useState<GameState>(
    initialDevState || "menu"
  );

  const [canvasDimsForStyle, setCanvasDimsForStyle] = useState({
    width: BASE_CANVAS_WIDTH,
    height: BASE_CANVAS_HEIGHT,
  });

  const setGameState = useCallback(
    (newState: GameState | ((prevState: GameState) => GameState)) => {
      _setGameState((prevState) => {
        const resolvedState =
          typeof newState === "function" ? newState(prevState) : newState;
        if (onGameStateChange) {
          onGameStateChange(resolvedState);
        }
        return resolvedState;
      });
    },
    [onGameStateChange]
  );

  useEffect(() => {
    if (onGameStateChange) {
      onGameStateChange(gameState);
    }
  }, [gameState, onGameStateChange]);

  const [score, setScore] = useState(0);
  const [level, setLevel] = useState(1);
  const [enemiesKilled, setEnemiesKilled] = useState(0);
  const [settings, setSettings] = useState<GameSettings>(DEFAULT_SETTINGS);
  const [blueskyHandle, setBlueskyHandle] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationError, setVerificationError] = useState("");
  const [showConfirmAnonymous, setShowConfirmAnonymous] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [showTopRankConsent, setShowTopRankConsent] = useState(false);
  const [isMaxStreakActive, setIsMaxStreakActive] = useState(false);
  const [borderColor, setBorderColor] = useState("transparent");

  const audioRefs = useRef<{
    playerShoot: HTMLAudioElement | null;
    playerHit: HTMLAudioElement | null;
    enemyHit: HTMLAudioElement | null;
    enemyKill: HTMLAudioElement | null;
    powerupPickup: HTMLAudioElement | null;
    backgroundMusic: HTMLAudioElement | null;
  }>({
    playerShoot: null,
    playerHit: null,
    enemyHit: null,
    enemyKill: null,
    powerupPickup: null,
    backgroundMusic: null,
  });

  const { isPaused, togglePause, setIsPaused } = usePauseGame(
    gameState,
    audioRefs,
    settings,
    // Reset the game's internal timer when unpausing to prevent anti-camping penalties
    () => {
      gameStateRef.current.lastTime = performance.now();
    }
  );

  const [savedScoreId, setSavedScoreId] = useState<Id<"scores"> | null>(null);
  const [leaderboardPosition, setLeaderboardPosition] = useState<
    number | string | null
  >(null); // For BlueskyShare
  const submitScoreAndCheckRank = useAction(api.index.submitScoreAndCheckRank);
  const postTopRankAnnouncement = useAction(api.social.postTopRankAnnouncement);
  const verifyBlueskyHandle = useAction(api.social.verifyBlueskyHandle);

  // Fetch rank when savedScoreId is available
  const fetchedRank = useQuery(
    api.index.getScoreRank,
    savedScoreId ? { scoreId: savedScoreId } : "skip"
  );

  useEffect(() => {
    if (fetchedRank !== undefined && fetchedRank !== null) {
      setLeaderboardPosition(fetchedRank);
    } else if (savedScoreId && fetchedRank === null) {
      // Score submitted but rank not found (edge case) or still loading
      setLeaderboardPosition("N/A"); // Or some loading indicator
    }
  }, [fetchedRank, savedScoreId]);

  const gameStateRef = useRef<GameInternalState>({
    player: {
      x: BASE_CANVAS_WIDTH / 2 - 25,
      y: BASE_CANVAS_HEIGHT - 60,
      width: 50,
      height: 30,
      health: 100,
      maxHealth: 100,
      fireRate: 300,
      lastShot: 0,
      powerLevel: 1,
      activeMultiShotLevel: 0,
      multiShotLevelExpireTime: 0,
      autoHealCharges: 0,
      lastSignificantMoveX: BASE_CANVAS_WIDTH / 2 - 25,
      timeStationaryMs: 0,
      isBlinkingRed: false,
      lastPenaltyAppliedAtSecondTier:
        INACTIVITY_CONSTANTS.PENALTY_START_THRESHOLD_S - 1,
      invincibilityEndTime: 0,
    } as Player,
    enemies: [] as Enemy[],
    bullets: [] as Bullet[],
    powerUps: [] as PowerUp[],
    particles: [] as Particle[],
    floatingTexts: [] as FloatingText[],
    explosions: [] as Explosion[],
    asteroids: [] as Asteroid[],
    keys: {} as Record<string, boolean>,
    lastEnemySpawn: 0,
    lastAsteroidSpawn: 0,
    lastSoloAsteroidSpawn: 0,
    enemySpawnRate: 2000,
    lastTime: 0,
    shakeIntensity: 0,
    shakeDecay: 0.9,
    isBossActiveRef: false,
    lastBossSpawnBlockRef: -1,
    globalBossPowerLevelRef: 2,
    barrierLine: 6,
    specialShip: null,
    crate: null,
    lastBarrierBoostSpawnLevelBlock: -1,
    lastAutoHealSpawnLevelBlock: -1,
    totalRunTimeMs: 0,
    gameSpeed: 1.0,
    timeDilationEndTime: 0,
    isMaxStreakActive: false,
  });

  useEffect(() => {
    const updateCanvasDimensions = () => {
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;

      const newEffectivelyMobile = screenWidth < 768 && !isMaximized;
      if (newEffectivelyMobile !== isMobile) {
        setIsMobile(newEffectivelyMobile);
      }

      let newWidth: number;
      let newHeight: number;

      if (isMaximized) {
        const aspectRatio = BASE_CANVAS_WIDTH / BASE_CANVAS_HEIGHT;
        if (screenWidth / screenHeight > aspectRatio) {
          newHeight = screenHeight;
          newWidth = newHeight * aspectRatio;
        } else {
          newWidth = screenWidth;
          newHeight = newWidth / aspectRatio;
        }
      } else if (newEffectivelyMobile) {
        const aspectRatio = BASE_CANVAS_WIDTH / BASE_CANVAS_HEIGHT;
        newWidth = screenWidth;
        newHeight = screenWidth / aspectRatio;
      } else {
        newWidth = BASE_CANVAS_WIDTH;
        newHeight = BASE_CANVAS_HEIGHT;
      }

      CANVAS_WIDTH = newWidth;
      CANVAS_HEIGHT = newHeight;
      canvasScale = CANVAS_WIDTH / BASE_CANVAS_WIDTH;

      setCanvasDimsForStyle((prevDims) => {
        if (prevDims.width === newWidth && prevDims.height === newHeight) {
          return prevDims;
        }
        return { width: newWidth, height: newHeight };
      });

      const canvasStyle = {
        width: `${newWidth}px`,
        height: `${newHeight}px`,
        position: "" as const,
        top: "",
        left: "",
        zIndex: "",
      };

      if (canvasRef.current) {
        canvasRef.current.width = newWidth;
        canvasRef.current.height = newHeight;
        Object.assign(canvasRef.current.style, canvasStyle);
      }
      if (overlayCanvasRef.current) {
        overlayCanvasRef.current.width = newWidth;
        overlayCanvasRef.current.height = newHeight;
        Object.assign(overlayCanvasRef.current.style, canvasStyle);
      }
    };

    updateCanvasDimensions();
    window.addEventListener("resize", updateCanvasDimensions);

    if (isMaximized || gameState === "playing") {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }

    return () => {
      window.removeEventListener("resize", updateCanvasDimensions);
      if (document.body && !isMaximized && gameState !== "playing") {
        document.body.style.overflow = "";
      } else if (document.body) {
        document.body.style.overflow = "hidden";
      }
    };
  }, [gameState, isMaximized, isMobile]);

  useEffect(() => {
    const loadAudio = (filename: string) => {
      const audio = new Audio(`/audio/${filename}`);
      audio.preload = "auto";
      audio.addEventListener("error", () =>
        console.warn(`Could not load audio file: ${filename}`)
      );
      return audio;
    };

    audioRefs.current = {
      playerShoot: loadAudio("player-shoot.mp3"),
      playerHit: loadAudio("player-hit.mp3"),
      enemyHit: loadAudio("enemy-hit.mp3"),
      enemyKill: loadAudio("enemy-kill.mp3"),
      powerupPickup: loadAudio("powerup-pickup.mp3"),
      backgroundMusic: loadAudio("background-music3.mp3"),
    };

    if (audioRefs.current.backgroundMusic) {
      audioRefs.current.backgroundMusic.loop = true;
    }
  }, []);

  useEffect(() => {
    const animateKeys = () => {
      if (Math.random() < 0.01) keyAnimationRef.current.left = Date.now();
      if (Math.random() < 0.01) keyAnimationRef.current.right = Date.now();
      if (Math.random() < 0.005) keyAnimationRef.current.space = Date.now();
    };
    const interval = setInterval(animateKeys, 100);
    return () => clearInterval(interval);
  }, []);

  const { getMovementKeys, keyMap: gameKeyMap } = useGameControls(settings);

  // Create game logic dependencies
  const gameLogicDependencies: GameLogicDependencies = {
    gameStateRef,
    audioRefs,
    upgradeManagerRef,
    backgroundAnimationRef,
    crtAnimationRef,
    settings,
    level,
    enemiesKilled,
    isMobile,
    setScore,
    setLevel,
    setEnemiesKilled,
    setGameState,
    setUpgradeOptions,
    setIsMaxStreakActive,
    getMovementKeys,
  };

  // Get the extracted game logic functions
  const gameLogicHooks = createGameLogicHooks(gameLogicDependencies);
  const { playSound, createParticles, checkCollision, createEnemy, spawnEnemies, spawnSpecialBonuses, updateGame } = gameLogicHooks;

  // Create renderer dependencies
  const rendererDependencies: RendererDependencies = {
    canvasRef,
    overlayCanvasRef,
    gameStateRef,
    backgroundAnimationRef,
    crtAnimationRef,
    isMobile,
    score,
    level,
    enemiesKilled,
    CANVAS_WIDTH,
    CANVAS_HEIGHT,
    canvasScale,
    BASE_CANVAS_WIDTH,
    BASE_CANVAS_HEIGHT,
  };

  // Get the extracted rendering functions
const renderingHooks = useRenderingHooks(rendererDependencies);
  const { renderCRTOverlay, render } = renderingHooks;

  const gameLoop = useCallback(
    (currentTime: number) => {
      if (gameState === "playing" && !isPaused) {
        updateGame(currentTime);
        render();
      }
      if (gameStateRef.current.isMaxStreakActive !== isMaxStreakActive) {
        setIsMaxStreakActive(gameStateRef.current.isMaxStreakActive);
      }
      animationRef.current = requestAnimationFrame(gameLoop);
    },
    [gameState, isPaused, updateGame, render, isMaxStreakActive]
  );

  const handleScoreSubmission = useCallback(
    async (submissionArgs: any) => {
      try {
        const result = await submitScoreAndCheckRank(submissionArgs);
        if (result && result.scoreId) {
          setSavedScoreId(result.scoreId);
          setLeaderboardPosition(result.rank);

          if (result.rank && result.rank >= 1 && result.rank <= 3) {
            setShowTopRankConsent(true);
          } else {
            setGameState("gameOver");
          }
        }
      } catch (e) {
        console.error("Failed to submit score and check rank:", e);
        setVerificationError("Failed to submit score.");
      } finally {
        setIsVerifying(false);
      }
    },
    [submitScoreAndCheckRank]
  );

  const handleSubmitScore = useCallback(async () => {
    if (!blueskyHandle.trim()) {
      setShowConfirmAnonymous(true);
      return;
    }
    setIsVerifying(true);
    setVerificationError("");
    try {
      const res = await verifyBlueskyHandle({ handle: blueskyHandle });
      if (res.valid && res.did) {
        await handleScoreSubmission({
          playerName: blueskyHandle,
          blueskyHandle,
          blueskyDid: res.did,
          score,
          level,
          enemiesKilled,
          gameDurationMs: gameStateRef.current.totalRunTimeMs,
          isAnonymous: false,
        });
      } else {
        setVerificationError(res.error || "Invalid handle");
        setIsVerifying(false);
      }
    } catch (e) {
      setVerificationError("Failed to verify handle");
      setIsVerifying(false);
    }
  }, [
    blueskyHandle,
    score,
    level,
    enemiesKilled,
    verifyBlueskyHandle,
    handleScoreSubmission,
  ]);

  const handleAnonymousSubmit = useCallback(async () => {
    setShowConfirmAnonymous(false);
    setIsVerifying(true);
    await handleScoreSubmission({
      playerName: "Anonymous",
      score,
      level,
      enemiesKilled,
      gameDurationMs: gameStateRef.current.totalRunTimeMs,
      isAnonymous: true,
    });
  }, [score, level, enemiesKilled, handleScoreSubmission]);

  const TopRankConsentDialog = () => (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[200]">
      <div className="bg-[var(--background-color-main)] border-2 border-[var(--red-main-ff0000)] p-8 rounded-lg text-center shadow-lg">
        <h2 className="text-2xl mb-4 text-white">
          Your score is{" "}
          <span className="text-[var(--red-main-ff0000)]">legendary!</span>
        </h2>
        <p className="mb-6 text-white">
          Sire, our community should be informed. Allow me please to post your
          victory
        </p>
        <div className="flex justify-center gap-4">
          <button
            className="auth-button bg-[var(--red-main-ff0000)] hover:bg-[var(--red-secondary-ff4444)] text-white font-bold py-2 px-4 rounded"
            onClick={() => {
              void (async () => {
                if (savedScoreId) {
                  await postTopRankAnnouncement({ scoreId: savedScoreId });
                }
                setShowTopRankConsent(false);
                setGameState("gameOver");
              })();
            }}
          >
            Yes
          </button>
          <button
            className="auth-button bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
            onClick={() => {
              setShowTopRankConsent(false);
              setGameState("gameOver");
            }}
          >
            No
          </button>
        </div>
      </div>
    </div>
  );

  const startGame = useCallback(() => {
    setGameState("playing");
    setScore(0);
    setLevel(1);
    setEnemiesKilled(0);
    setBlueskyHandle("");
    setVerificationError("");
    setShowConfirmAnonymous(false);
    setSavedScoreId(null);
    setLeaderboardPosition(null);
    setIsPaused(false);
    upgradeManagerRef.current = new UpgradeManager();
    gameStateRef.current = {
      player: {
        x: BASE_CANVAS_WIDTH / 2 - 25,
        y: BASE_CANVAS_HEIGHT - 60,
        width: 50,
        height: 30,
        health: 100,
        maxHealth: 100,
        fireRate: 300,
        lastShot: 0,
        powerLevel: 1,
        activeMultiShotLevel: 0,
        multiShotLevelExpireTime: 0,
        autoHealCharges: 0,
        lastSignificantMoveX: BASE_CANVAS_WIDTH / 2 - 25,
        timeStationaryMs: 0,
        isBlinkingRed: false,
        lastPenaltyAppliedAtSecondTier:
          INACTIVITY_CONSTANTS.PENALTY_START_THRESHOLD_S - 1,
        invincibilityEndTime: 0,
        killStreak: 0,
      },
      enemies: [],
      bullets: [],
      powerUps: [],
      particles: [],
      floatingTexts: [],
      explosions: [],
      asteroids: [],
      keys: {},
      lastEnemySpawn: 0,
      lastAsteroidSpawn: 0,
      lastSoloAsteroidSpawn: 0,
      enemySpawnRate: 2000,
      lastTime: performance.now(),
      shakeIntensity: 0,
      shakeDecay: 0.9,
      isBossActiveRef: false,
      lastBossSpawnBlockRef: -1,
      globalBossPowerLevelRef: 2,
      barrierLine: 6,
      specialShip: null,
      crate: null,
      lastBarrierBoostSpawnLevelBlock: -1,
      lastAutoHealSpawnLevelBlock: -1,
      totalRunTimeMs: 0,
      gameSpeed: 1.0,
      timeDilationEndTime: 0,
      isMaxStreakActive: false,
    };
    playSound("backgroundMusic");
    if (animationRef.current) cancelAnimationFrame(animationRef.current);
    animationRef.current = requestAnimationFrame(gameLoop);
  }, [gameLoop, playSound, setIsPaused]);

  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      if (isMobile) return;

      gameStateRef.current.keys[e.key] = true;
      if (e.key === " ") e.preventDefault();
    };
    const onKeyUp = (e: KeyboardEvent) => {
      if (isMobile) return;
      gameStateRef.current.keys[e.key] = false;
    };
    window.addEventListener("keydown", onKeyDown);
    window.addEventListener("keyup", onKeyUp);
    return () => {
      window.removeEventListener("keydown", onKeyDown);
      window.removeEventListener("keyup", onKeyUp);
    };
  }, [isMobile, gameState]);

  useEffect(() => {
    if (gameState === "playing" && !animationRef.current) {
      animationRef.current = requestAnimationFrame(gameLoop);
    } else if (gameState !== "playing" && animationRef.current) {
      cancelAnimationFrame(animationRef.current);
      animationRef.current = null;
    }
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = null;
      }
    };
  }, [gameState, gameLoop]);

  useEffect(() => {
    if (initialDevState === "playing" && gameState === "playing") {
      console.log(
        '[DevUtils] initialDevState is "playing", calling startGame() to ensure full initialization...'
      );
      startGame();
    }
  }, [initialDevState, gameState, startGame, isMaximized]);

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (isMaxStreakActive) {
      setBorderColor("var(--red-main-ff0000)"); // Start with red
      interval = setInterval(() => {
        setBorderColor((prev) =>
          prev === "white" ? "var(--red-main-ff0000)" : "white"
        );
      }, 250);
    } else {
      setBorderColor("transparent");
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isMaxStreakActive]);

  const handlePlayerMove = useCallback(
    (position: number) => {
      if (!isMobile || gameState !== "playing" || isPaused) return;

      const player = gameStateRef.current.player;
      const oldX = player.x;

      // Convert percentage to actual position
      const targetX = (position / 100) * BASE_CANVAS_WIDTH - player.width / 2;
      player.x = Math.max(
        0,
        Math.min(targetX, BASE_CANVAS_WIDTH - player.width)
      );

      // Reset anti-camping timer when actual movement occurs
      if (Math.abs(player.x - oldX) > 0) {
        player.timeStationaryMs = 0;
        player.isBlinkingRed = false;
        if (
          Math.abs(player.x - player.lastSignificantMoveX) >=
          INACTIVITY_CONSTANTS.SIGNIFICANT_MOVE_PX
        ) {
          player.lastSignificantMoveX = player.x;
          player.lastPenaltyAppliedAtSecondTier =
            INACTIVITY_CONSTANTS.PENALTY_START_THRESHOLD_S - 1;
        }
      }
    },
    [isMobile, gameState, isPaused]
  );

  const handleCanvasToggleMaximized = useCallback(() => {
    setIsMaximized(!isMaximized);
  }, [isMaximized, setIsMaximized]);

  const handleUpgradeSelected = (upgrade: UpgradeDefinition) => {
    upgradeManagerRef.current.levelUp(upgrade.id);

    if (upgrade.id === "healthyBastard") {
      gameStateRef.current.player.maxHealth += 10;
      gameStateRef.current.player.health += 10;
    }

    // Trigger time dilation
    gameStateRef.current.timeDilationEndTime = performance.now() + 5000;

    // Reset lastTime to prevent a large dt after the upgrade screen, which could trigger the anti-camp system unfairly.
    gameStateRef.current.lastTime = performance.now();
    setGameState("playing");
  };

  if (gameState === "upgrading") {
    return (
      <UpgradeScreen
        upgrades={upgradeOptions}
        onSelect={handleUpgradeSelected}
        getUpgradeLevel={(id) => upgradeManagerRef.current.getUpgradeLevel(id)}
      />
    );
  }

  if (showTopRankConsent) return <TopRankConsentDialog />;
  if (gameState === "submitScore")
    return (
      <ScoreSubmissionScreen
        {...{
          blueskyHandle,
          setBlueskyHandle,
          isVerifying,
          verificationError,
          handleSubmitScore,
          showConfirmAnonymous,
          setShowConfirmAnonymous,
          handleAnonymousSubmit,
        }}
      />
    );
  if (gameState === "options")
    return (
      <OptionsScreen
        {...{
          settings,
          setSettings,
          setGameState,
          isMaximized,
          onToggleMaximized: handleCanvasToggleMaximized,
        }}
      />
    );
  if (gameState === "leaderboard")
    return <LeaderboardScreen setGameState={setGameState} />;
  if (gameState === "menu")
    return <MenuScreen startGame={startGame} setGameState={setGameState} />;
  if (gameState === "gameOver")
    return (
      <div
        className="flex flex-col items-center justify-center min-h-screen bg-black text-[var(--red-main-ff0000)]"
        style={{ fontFamily: "VT323, monospace" }}
      >
        <GameOverScreen
          {...{
            score,
            level,
            enemiesKilled,
            savedScoreId,
            startGame,
            setGameState,
          }}
        />
        {savedScoreId && blueskyHandle && (
          <div className="mt-8">
            <BlueskyShare
              scoreId={savedScoreId}
              blueskyHandle={blueskyHandle}
              score={score}
              playerLevel={level}
              enemiesKilled={enemiesKilled}
              leaderboardPosition={
                typeof leaderboardPosition === "number"
                  ? leaderboardPosition
                  : undefined
              }
              gameDuration={formatGameDuration(
                gameStateRef.current.totalRunTimeMs
              )}
              onClose={() => {
                setSavedScoreId(null);
                setLeaderboardPosition(null);
                setGameState("menu");
              }}
            />
          </div>
        )}
      </div>
    );

  return (
    <div
      className={`flex flex-col min-h-screen ${isMobile ? "items-around justify-start" : "items-center justify-center"}`}
      style={{ fontFamily: "VT323, monospace" }}
    >
      {!isMobile && (
        <div className="lg:max-w-38 flex-shrink-0">
          <PowerUpLegend
            autoHealCharges={gameStateRef.current.player.autoHealCharges}
          />
        </div>
      )}
      <div
        className={`flex items-center gap-2 z-10 relative ${isMobile ? "flex-col h-full top-0" : "flex-row"}`}
        style={{
          ["--canvas-width" as string]: `${canvasDimsForStyle.width}px`,
          ["--canvas-height" as string]: `${canvasDimsForStyle.height}px`,
        }}
      >
        <div
          className="relative flex-grow" // This container establishes the stacking context and allows it to grow
          style={{
            width: canvasDimsForStyle.width,
            height: canvasDimsForStyle.height,
            boxShadow: `0 0 10px 5px ${borderColor}`,
            transition: "box-shadow 0.2s linear",
          }}
        >
          <GameScreen
            canvasRef={canvasRef}
            overlayCanvasRef={overlayCanvasRef}
            isMobile={isMobile}
            isMaximized={isMaximized}
          />
          {gameState === "playing" && (
            <GameHUD
              score={score}
              level={level}
              enemiesKilled={enemiesKilled}
              gameTimeMs={gameStateRef.current.totalRunTimeMs}
              isMobile={isMobile}
              canvasWidth={canvasDimsForStyle.width}
              canvasHeight={canvasDimsForStyle.height}
              lastLevel={level > 1 ? level - 1 : 1} // Pass previous level for animation
            />
          )}
          {gameState === "playing" && <PauseButton onClick={togglePause} />}
        </div>

        {!isMobile && (
          <div
            id="if-max-hide"
            className={`w-32 flex-shrink-0 ${isMaximized ? "hidden" : ""}`}
          >
            <ControlsDisplay
              {...{
                keyAnimationRef,
                settings,
                getMovementKeys,
                keyMap: gameKeyMap,
              }}
            />
          </div>
        )}

        {isMobile && gameState === "playing" && (
          <MobileCanvas onPlayerMove={handlePlayerMove} />
        )}

        <PauseMenu
          isPaused={isPaused && gameState === "playing"}
          onResume={togglePause}
          onSettings={() => {
            setIsPaused(false);
            setGameState("options");
          }}
          onQuit={() => {
            setIsPaused(false);
            setGameState("menu");
            if (audioRefs.current.backgroundMusic)
              audioRefs.current.backgroundMusic.pause();
          }}
        />
      </div>
    </div>
  );
}

